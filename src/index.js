/**
 * Google Drive Excel 提取器
 *
 * 這個Cloudflare Worker允許用戶連接到Google Drive
 * 並提取Excel文件中的數據
 */

import customersData from './data/customers.json';
import productsData from './data/products.json';

// 處理HTTP請求的主要入口點
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    // 主頁 - 顯示登入按鈕
    if (path === "/" || path === "") {
      return new Response(getHomePage(), {
        headers: { "Content-Type": "text/html" },
      });
    }

    // 處理OAuth回調
    if (path === "/oauth/callback") {
      return handleOAuthCallback(request, env);
    }

    // 處理認證，生成Google Drive授權URL
    if (path === "/auth") {
      return handleAuth(env);
    }

    // 列出Google Drive中的Excel文件
    if (path === "/list-files") {
      return handleListFiles(request, env);
    }

    // 提取Excel文件數據
    if (path === "/extract") {
      return handleExtractExcel(request, env);
    }

    // 客戶表單頁面
    if (path === "/customer-form") {
      return handleCustomerForm(request, env);
    }

    // 創建出貨記錄頁面
    if (path === "/create-invoice") {
      return handleCreateInvoice(request, env);
    }

    // 提交出貨記錄
    if (path === "/submit-invoice") {
      return handleSubmitInvoice(request, env);
    }

    // 預覽出貨單
    if (path === "/preview-invoice") {
      return handlePreviewInvoice(request, env);
    }

    // API: 獲取最後一筆餘額
    if (path === "/api/get-last-balance") {
      return handleGetLastBalance(request, env);
    }

    // API: 獲取商品清單
    if (path === "/api/get-products") {
      return handleGetProducts(request, env);
    }

    // 所有其他路徑返回404
    return new Response("Not found", { status: 404 });
  },
};

// 返回簡單的HTML主頁，包含登入按鈕
function getHomePage() {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Google Drive Excel 提取器</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
          }
          button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
          }
          button:hover {
            background-color: #3367d6;
          }
          .container {
            margin-top: 50px;
            text-align: center;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>📋 出貨單系統</h1>
          <p>這個系統可以幫助您管理藥局出貨單，並從 Google Sheets 中讀取真實的餘額資料</p>
          <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <strong>授權後可以：</strong><br>
            • 從真實的 Google Sheets 讀取前期餘額<br>
            • 自動更新客戶餘額資料<br>
            • 完整的出貨單管理功能
          </div>
          <button onclick="window.location.href='/auth'" style="margin: 10px;">🔐 授權 Google Drive 存取</button>
          <button onclick="window.location.href='/customer-form'" style="margin: 10px; background-color: #6c757d;">🚀 直接使用（模擬資料）</button>
        </div>
      </body>
    </html>
  `;
}

// 處理認證並重定向到Google的OAuth頁面
async function handleAuth(env) {
  const clientId = env.CLIENT_ID;
  const redirectUri = env.REDIRECT_URI;

  // Google OAuth 2.0 scope for Google Sheets - 使用更廣泛的權限
  const scopes = [
    'https://www.googleapis.com/auth/spreadsheets',
    'https://www.googleapis.com/auth/drive',
    'https://www.googleapis.com/auth/drive.readonly'
  ].join(' ');

  const authUrl = new URL('https://accounts.google.com/o/oauth2/auth');
  authUrl.searchParams.set('client_id', clientId);
  authUrl.searchParams.set('redirect_uri', redirectUri);
  authUrl.searchParams.set('response_type', 'code');
  authUrl.searchParams.set('scope', scopes);
  authUrl.searchParams.set('access_type', 'offline');
  authUrl.searchParams.set('prompt', 'consent');

  return Response.redirect(authUrl.toString(), 302);
}

// 處理從Google重定向回來的OAuth回調
async function handleOAuthCallback(request, env) {
  const url = new URL(request.url);
  const code = url.searchParams.get('code');
  const error = url.searchParams.get('error');

  if (error) {
    return new Response(`OAuth認證錯誤: ${error}`, { status: 400 });
  }

  if (!code) {
    return new Response('未收到授權碼', { status: 400 });
  }

  try {
    // 交換授權碼為存取令牌
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: env.CLIENT_ID,
        client_secret: env.CLIENT_SECRET,
        code: code,
        grant_type: 'authorization_code',
        redirect_uri: env.REDIRECT_URI,
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      return new Response(`令牌交換失敗: ${errorText}`, { status: 400 });
    }

    const tokens = await tokenResponse.json();

    // 將令牌存儲在cookie中和localStorage中
    const response = new Response(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>授權成功</title>
          <meta charset="UTF-8">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .success { color: #28a745; }
            .token-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <h1 class="success">✅ 授權成功！</h1>
          <p>您已成功連接到 Google Drive。現在可以讀取真實的 Google Sheets 資料了。</p>
          <div class="token-info">
            <small>Token已保存，有效期：${tokens.expires_in || 3600}秒</small>
          </div>
          <button onclick="window.location.href='/customer-form'" style="background: #007bff; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;">
            前往出貨單系統
          </button>

          <script>
            // 同時存儲在localStorage中作為備用
            localStorage.setItem('access_token', '${tokens.access_token}');
            localStorage.setItem('token_expires', Date.now() + ${(tokens.expires_in || 3600) * 1000});
            if ('${tokens.refresh_token}') {
              localStorage.setItem('refresh_token', '${tokens.refresh_token}');
            }
          </script>
        </body>
      </html>
    `, {
      headers: {
        'Content-Type': 'text/html',
        'Set-Cookie': [
          `access_token=${tokens.access_token}; Path=/; Max-Age=${tokens.expires_in || 3600}; SameSite=Lax`,
          `token_type=${tokens.token_type || 'Bearer'}; Path=/; Max-Age=${tokens.expires_in || 3600}; SameSite=Lax`
        ]
      }
    });

    return response;
  } catch (error) {
    return new Response(`OAuth處理錯誤: ${error.message}`, { status: 500 });
  }
}

// 列出用戶Google Drive中的Excel文件
async function handleListFiles(request, env) {
  // 這裡將列出Google Drive中的文件
  // 目前是一個簡單的佔位符
  return new Response("列出文件功能尚未實現", { status: 501 });
}

// 從指定的Excel文件中提取數據並顯示嵌入式編輯器
async function handleExtractExcel(request, env) {
  try {
    const url = new URL(request.url);
    const fileId = url.searchParams.get('fileId');
    const clientName = url.searchParams.get('clientName');

    if (!fileId) {
      return new Response(JSON.stringify({ error: '缺少 fileId 參數' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 返回包含嵌入式 Google Sheets 的 HTML 頁面
    const html = getEmbeddedSheetPage(fileId, clientName);

    return new Response(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });

  } catch (error) {
    console.error('顯示Excel編輯器時發生錯誤:', error);
    return new Response(JSON.stringify({
      error: '顯示Excel編輯器時發生錯誤',
      details: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// 根據客戶ID獲取對應的檔案ID（從JSON資料讀取）
function getFileIdForCustomerId(customerId) {
  for (const company of customersData.companies) {
    const client = company.clients.find(c => c.id === customerId);
    if (client && client.fileId) {
      return client.fileId;
    }
  }
  return '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ'; // 預設檔案ID
}

// 處理客戶表單頁面
async function handleCustomerForm(request, env) {

  // 從 JSON 檔案讀取所有客戶
  const allCustomers = [];
  customersData.companies.forEach(company => {
    company.clients.forEach(client => {
      allCustomers.push({
        company: company.name,
        name: client.name,
        id: client.id,
        fileId: client.fileId || '請設定檔案ID'
      });
    });
  });

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>選擇客戶 - 出貨單系統</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
          }

          .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            padding: 40px;
            max-width: 500px;
            width: 100%;
          }

          h1 {
            color: #333;
            text-align: center;
            margin-bottom: 10px;
            font-size: 28px;
          }

          .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
          }

          .form-group {
            margin-bottom: 25px;
          }

          label {
            display: block;
            color: #555;
            font-weight: 500;
            margin-bottom: 10px;
            font-size: 14px;
          }

          select, input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
          }

          select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }

          .company-section {
            margin-bottom: 25px;
          }

          .company-title {
            color: #555;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            padding-left: 5px;
            border-left: 3px solid #667eea;
          }

          .customer-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 15px;
          }

          .customer-card {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
          }

          .customer-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);
          }

          .customer-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
          }

          .customer-actions {
            display: flex;
            gap: 8px;
          }

          .btn-action {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
          }

          .btn-view {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
          }

          .btn-view:hover {
            background: #e9ecef;
            transform: translateY(-1px);
          }

          .btn-create {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }

          .btn-create:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }

          .submit-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 20px;
          }

          .submit-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
          }

          .submit-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .divider {
            text-align: center;
            color: #999;
            margin: 30px 0;
            position: relative;
          }

          .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
          }

          .divider span {
            background: white;
            padding: 0 15px;
            position: relative;
          }

          .custom-input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
          }

          .custom-input-group input {
            flex: 1;
          }

          .custom-input-group button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
          }

          .custom-input-group button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
          }

          @media (max-width: 480px) {
            .container {
              padding: 30px 20px;
            }

            h1 {
              font-size: 24px;
            }

            .customer-list {
              grid-template-columns: 1fr;
            }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>📋 出貨單系統</h1>
          <p class="subtitle">選擇客戶以開啟對應的出貨單</p>

          <div class="form-group">
            <label for="fileId">Google Sheets 檔案 ID</label>
            <input
              type="text"
              id="fileId"
              name="fileId"
              value="每個客戶有獨立的檔案ID"
              readonly
              placeholder="輸入 Google Sheets 檔案 ID"
            />
          </div>

          <div class="divider">
            <span>快速選擇客戶</span>
          </div>

          ${customersData.companies.map(company => `
            <div class="company-section">
              <h3 class="company-title">${company.name}</h3>
              <div class="customer-list">
                ${company.clients.map(client => {
    return `
                  <div class="customer-card">
                    <div class="customer-name">${client.name}</div>
                    <div class="customer-id" style="font-size: 12px; color: #666; margin: 5px 0;">
                      檔案ID: ${client.fileId || '請設定檔案ID'}
                    </div>
                    <div class="customer-actions">
                      <a href="/extract?fileId=${client.fileId}&clientName=${encodeURIComponent(client.name)}"
                         class="btn-action btn-view">
                        📊 查看記錄
                      </a>
                      <a href="/create-invoice?fileId=${client.fileId}&clientName=${encodeURIComponent(client.name)}"
                         class="btn-action btn-create">
                        📝 建立出貨單
                      </a>
                    </div>
                  </div>
                  `;
  }).join('')}
              </div>
            </div>
          `).join('')}

          <div class="divider">
            <span>或輸入自訂客戶名稱</span>
          </div>

          <div class="form-group">
            <label for="customClient">自訂客戶名稱</label>
            <div class="custom-input-group">
              <input
                type="text"
                id="customClient"
                name="customClient"
                placeholder="輸入客戶名稱"
              />
              <button onclick="goToCustomClient()">開啟</button>
            </div>
          </div>
        </div>

        <script>
          // 每個客戶現在都有固定的檔案ID，不需要動態更新連結

          // 前往自訂客戶（使用預設檔案ID）
          function goToCustomClient() {
            const clientName = document.getElementById('customClient').value;

            if (!clientName.trim()) {
              alert('請輸入客戶名稱');
              return;
            }

            // 使用預設檔案ID作為自訂客戶
            const defaultFileId = '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ';
            window.location.href = \`/extract?fileId=\${encodeURIComponent(defaultFileId)}&clientName=\${encodeURIComponent(clientName)}\`;
          }

          // Enter 鍵送出
          document.getElementById('customClient').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
              goToCustomClient();
            }
          });
        </script>
      </body>
    </html>
  `;

  return new Response(html, {
    status: 200,
    headers: {
      'Content-Type': 'text/html; charset=utf-8'
    }
  });
}

// 處理創建出貨記錄頁面
async function handleCreateInvoice(request, env) {
  const url = new URL(request.url);
  const clientName = url.searchParams.get('clientName') || '';
  const fileId = url.searchParams.get('fileId') || env.DEFAULT_FILE_ID || '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ';

  // 根據客戶名稱找出所屬公司和客戶ID
  let companyId = '';
  let companyName = '';
  let customerId = '';

  for (const company of customersData.companies) {
    const client = company.clients.find(c => c.name === clientName);
    if (client) {
      companyId = company.id;
      companyName = company.name;
      customerId = client.id;
      break;
    }
  }

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>創建出貨記錄 - ${clientName}</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            padding: 20px;
          }

          .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
          }

          .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
          }

          .form-section {
            padding: 30px;
          }

          .form-group {
            margin-bottom: 20px;
          }

          label {
            display: block;
            color: #555;
            font-weight: 500;
            margin-bottom: 8px;
          }

          input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
          }

          input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
          }

          .row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
          }

          .items-section {
            border-top: 1px solid #e1e5e9;
            padding-top: 20px;
            margin-top: 20px;
          }

          .item-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 10px;
            align-items: end;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
          }

          .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
          }

          .btn-primary {
            background: #667eea;
            color: white;
          }

          .btn-success {
            background: #28a745;
            color: white;
          }

          .btn-danger {
            background: #dc3545;
            color: white;
          }

          .btn-secondary {
            background: #6c757d;
            color: white;
          }

          .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          }

          .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
          }

          .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }

          .total {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            border-top: 2px solid #667eea;
            padding-top: 10px;
          }

          @media (max-width: 768px) {
            .row {
              grid-template-columns: 1fr;
            }

            .item-row {
              grid-template-columns: 1fr;
            }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📋 創建出貨記錄</h1>
            <p>客戶：${clientName} (${companyName})</p>
          </div>

          <div class="form-section">
            <form id="invoiceForm">
              <input type="hidden" name="fileId" value="${fileId}">
              <input type="hidden" name="clientName" value="${clientName}">
              <input type="hidden" name="companyId" value="${companyId}">
              <input type="hidden" name="companyName" value="${companyName}">

              <div class="row">
                <div class="form-group">
                  <label for="invoiceDate">出貨日期</label>
                  <input type="date" id="invoiceDate" name="invoiceDate" required>
                </div>
                <div class="form-group">
                  <label for="shippingFee">郵費</label>
                  <input type="number" id="shippingFee" name="shippingFee" min="0" value="0" onchange="calculateTotal()">
                </div>
              </div>

              <div class="row">
                <div class="form-group">
                  <label for="previousBalance">前期餘額</label>
                  <div style="display: flex; gap: 10px; align-items: center;">
                    <input type="number" id="previousBalance" name="previousBalance" step="0.01" value="0" onchange="calculateTotal()" readonly style="flex: 1;">
                    <button type="button" onclick="fetchLastBalance()" style="padding: 8px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">從Excel抓取</button>
                  </div>
                </div>
                <div class="form-group">
                  <label for="paidAmount">本次餘額</label>
                  <input type="number" id="paidAmount" name="paidAmount" min="0" step="0.01" value="0" onchange="calculateTotal()">
                </div>
              </div>

              <div class="items-section">
                <h3>商品明細</h3>
                <div id="itemsList">
                  <div class="item-row">
                    <div>
                      <label>商品名稱</label>
                      <select name="itemName[]" required onchange="onProductChange(this)">
                        <option value="">請選擇商品</option>
                      </select>
                    </div>
                    <div>
                      <label>數量</label>
                      <input type="number" name="quantity[]" min="1" value="1" required onchange="calculateTotal()">
                    </div>
                    <div>
                      <label>單價</label>
                      <input type="number" name="unitPrice[]" min="0" step="0.01" required onchange="calculateTotal()" readonly>
                    </div>
                    <div>
                      <label>金額</label>
                      <input type="number" name="amount[]" readonly>
                    </div>
                    <div>
                      <label>&nbsp;</label>
                      <button type="button" class="btn btn-danger" onclick="removeItem(this)">刪除</button>
                    </div>
                  </div>
                </div>

                <button type="button" class="btn btn-secondary" onclick="addItem()">+ 新增商品</button>
              </div>

              <div class="form-group">
                <label for="notes">備註</label>
                <textarea id="notes" name="notes" rows="3" placeholder="輸入備註資訊"></textarea>
              </div>

              <div class="summary">
                <div class="summary-row">
                  <span>商品小計：</span>
                  <span id="subtotal">$0</span>
                </div>
                <div class="summary-row">
                  <span>郵費：</span>
                  <span id="shippingDisplay">$0</span>
                </div>
                <div class="summary-row total">
                  <span>本次應付總計：</span>
                  <span id="total">$0</span>
                </div>
                <div style="margin: 20px 0; border-top: 1px dashed #dee2e6;"></div>
                <div class="summary-row">
                  <span>前期餘額：</span>
                  <span id="previousBalanceDisplay">$0</span>
                </div>
                <div class="summary-row">
                  <span>減：本次應付：</span>
                  <span id="currentTotalDisplay">$0</span>
                </div>
                <div class="summary-row">
                  <span>本次餘額：</span>
                  <span id="paidAmountDisplay">$0</span>
                </div>
              </div>

              <div style="margin-top: 30px; text-align: center;">
                <button type="submit" class="btn btn-success" style="margin-right: 10px;">💾 儲存記錄</button>
                <button type="button" class="btn btn-primary" onclick="previewInvoice()">👁️ 預覽出貨單</button>
              </div>
            </form>
          </div>
        </div>

        <script>
          // 商品資料
          let productsData = [];

          // 設定今天的日期為預設值
          document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

          // 載入商品資料
          async function loadProducts() {
            try {
              const response = await fetch('/api/get-products');
              const result = await response.json();
              if (result.success) {
                productsData = result.products;
                populateProductOptions();
              }
            } catch (error) {
              console.error('載入商品資料失敗:', error);
            }
          }

          // 填充商品選項到所有下拉選單
          function populateProductOptions() {
            const selects = document.querySelectorAll('select[name="itemName[]"]');
            selects.forEach(select => {
              // 保留目前選擇的值
              const currentValue = select.value;

              // 清空選項並重新填充
              select.innerHTML = '<option value="">請選擇商品</option>';

              productsData.forEach(product => {
                const option = document.createElement('option');
                option.value = product.name;
                option.textContent = \`\${product.name} - $\${product.price} (\${product.unit})\`;
                option.dataset.price = product.price;
                option.dataset.productId = product.id;
                select.appendChild(option);
              });

              // 恢復之前的選擇
              if (currentValue) {
                select.value = currentValue;
              }
            });
          }

          // 商品選擇變更時的處理
          function onProductChange(selectElement) {
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            const priceInput = selectElement.closest('.item-row').querySelector('input[name="unitPrice[]"]');

            if (selectedOption.dataset.price) {
              priceInput.value = selectedOption.dataset.price;
            } else {
              priceInput.value = '';
            }

            calculateTotal();
          }

          function addItem() {
            const itemsList = document.getElementById('itemsList');
            const newItem = document.createElement('div');
            newItem.className = 'item-row';
            newItem.innerHTML = \`
              <div>
                <label>商品名稱</label>
                <select name="itemName[]" required onchange="onProductChange(this)">
                  <option value="">請選擇商品</option>
                </select>
              </div>
              <div>
                <label>數量</label>
                <input type="number" name="quantity[]" min="1" value="1" required onchange="calculateTotal()">
              </div>
              <div>
                <label>單價</label>
                <input type="number" name="unitPrice[]" min="0" step="0.01" required onchange="calculateTotal()" readonly>
              </div>
              <div>
                <label>金額</label>
                <input type="number" name="amount[]" readonly>
              </div>
              <div>
                <label>&nbsp;</label>
                <button type="button" class="btn btn-danger" onclick="removeItem(this)">刪除</button>
              </div>
            \`;
            itemsList.appendChild(newItem);

            // 為新增的下拉選單填充商品選項
            populateProductOptions();
          }

          function removeItem(button) {
            if (document.querySelectorAll('.item-row').length > 1) {
              button.closest('.item-row').remove();
              calculateTotal();
            }
          }

          async function fetchLastBalance() {
            const customerId = '${customerId}';
            const fileId = getFileIdForCustomer(customerId);

            if (!fileId) {
              alert('找不到對應的Excel檔案ID');
              return;
            }

            try {
              // 準備headers，包括可能的access token
              const headers = {
                'Content-Type': 'application/json'
              };

              // 嘗試從localStorage獲取access token
              const accessToken = localStorage.getItem('access_token');
              if (accessToken) {
                headers['Authorization'] = 'Bearer ' + accessToken;
              }

              // 調用後端API來抓取最後一筆餘額
              const response = await fetch('/api/get-last-balance', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                  fileId: fileId,
                  customerId: customerId
                })
              });

              if (response.ok) {
                const result = await response.json();
                document.getElementById('previousBalance').value = result.lastBalance || 0;

                // 設定表單中的隱藏 fileId 欄位
                const fileIdInput = document.querySelector('input[name="fileId"]');
                if (fileIdInput) {
                  fileIdInput.value = fileId;
                }

                calculateTotal();
              } else {
                alert('抓取餘額失敗: ' + response.statusText);
              }
            } catch (error) {
              alert('抓取餘額時發生錯誤: ' + error.message);
            }
          }

          function getFileIdForCustomer(customerId) {
            // 從客戶資料中讀取對應的檔案ID
            const customersData = ${JSON.stringify(customersData)};
            for (const company of customersData.companies) {
              const client = company.clients.find(c => c.id === customerId);
              if (client && client.fileId) {
                return client.fileId;
              }
            }
            return '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ'; // 預設檔案ID
          }

          function calculateTotal() {
            let subtotal = 0;
            const itemRows = document.querySelectorAll('.item-row');

            itemRows.forEach(row => {
              const quantity = parseFloat(row.querySelector('input[name="quantity[]"]').value) || 0;
              const unitPrice = parseFloat(row.querySelector('input[name="unitPrice[]"]').value) || 0;
              const amount = quantity * unitPrice;

              row.querySelector('input[name="amount[]"]').value = amount.toFixed(2);
              subtotal += amount;
            });

            const shippingFee = parseFloat(document.getElementById('shippingFee').value) || 0;
            const total = subtotal + shippingFee;
            const previousBalance = parseFloat(document.getElementById('previousBalance').value) || 0;
            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;

            // 餘額計算：前期餘額 - 本次應付總計 = 本次餘額
            const balance = previousBalance - total;

            // 更新顯示
            document.getElementById('subtotal').textContent = '$' + subtotal.toFixed(2);
            document.getElementById('shippingDisplay').textContent = '$' + shippingFee.toFixed(2);
            document.getElementById('total').textContent = '$' + total.toFixed(2);
            document.getElementById('previousBalanceDisplay').textContent = '$' + previousBalance.toFixed(2);
            document.getElementById('currentTotalDisplay').textContent = '$' + total.toFixed(2);
            document.getElementById('paidAmountDisplay').textContent = '$' + balance.toFixed(2);

            // 同步本次餘額到最上面的輸入欄位（暫時移除事件監聽器避免循環）
            const paidAmountElement = document.getElementById('paidAmount');
            paidAmountElement.onchange = null;
            paidAmountElement.value = balance.toFixed(2);
            paidAmountElement.onchange = calculateTotal;

          }

          document.getElementById('invoiceForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // 收集表單資料
            const formData = new FormData(this);
            const data = {};

            // 基本資料
            data.fileId = formData.get('fileId');
            data.clientName = formData.get('clientName');
            data.companyId = formData.get('companyId');
            data.companyName = formData.get('companyName');
            data.invoiceDate = formData.get('invoiceDate');
            data.shippingFee = parseFloat(formData.get('shippingFee')) || 0;
            data.previousBalance = parseFloat(formData.get('previousBalance')) || 0;
            data.paidAmount = parseFloat(formData.get('paidAmount')) || 0;
            data.notes = formData.get('notes');

            // 商品資料
            data.items = [];
            const itemNames = formData.getAll('itemName[]');
            const quantities = formData.getAll('quantity[]');
            const unitPrices = formData.getAll('unitPrice[]');

            for (let i = 0; i < itemNames.length; i++) {
              if (itemNames[i].trim()) {
                data.items.push({
                  name: itemNames[i],
                  quantity: parseFloat(quantities[i]),
                  unitPrice: parseFloat(unitPrices[i]),
                  amount: parseFloat(quantities[i]) * parseFloat(unitPrices[i])
                });
              }
            }

            // 計算總計和餘額
            data.subtotal = data.items.reduce((sum, item) => sum + item.amount, 0);
            data.total = data.subtotal + data.shippingFee;
            data.balance = (data.previousBalance || 0) - data.total;

            // 提交資料
            fetch('/submit-invoice', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
              if (result.success) {
                alert('出貨記錄已成功儲存！');
                // 可以重新導向或清空表單
                window.location.href = '/customer-form';
              } else {
                alert('儲存失敗：' + result.error);
              }
            })
            .catch(error => {
              alert('提交時發生錯誤：' + error.message);
            });
          });

          function previewInvoice() {
            // 收集表單資料並開啟預覽
            const form = document.getElementById('invoiceForm');
            const formData = new FormData(form);
            const data = {};

            // 基本資料
            data.fileId = formData.get('fileId');
            data.clientName = formData.get('clientName');
            data.companyId = formData.get('companyId');
            data.companyName = formData.get('companyName');
            data.invoiceDate = formData.get('invoiceDate');
            data.shippingFee = parseFloat(formData.get('shippingFee')) || 0;
            data.previousBalance = parseFloat(formData.get('previousBalance')) || 0;
            data.paidAmount = parseFloat(formData.get('paidAmount')) || 0;
            data.notes = formData.get('notes');

            // 商品資料
            data.items = [];
            const itemNames = formData.getAll('itemName[]');
            const quantities = formData.getAll('quantity[]');
            const unitPrices = formData.getAll('unitPrice[]');

            for (let i = 0; i < itemNames.length; i++) {
              if (itemNames[i].trim()) {
                data.items.push({
                  name: itemNames[i],
                  quantity: parseFloat(quantities[i]),
                  unitPrice: parseFloat(unitPrices[i]),
                  amount: parseFloat(quantities[i]) * parseFloat(unitPrices[i])
                });
              }
            }

            // 計算總計和餘額
            data.subtotal = data.items.reduce((sum, item) => sum + item.amount, 0);
            data.total = data.subtotal + data.shippingFee;
            data.balance = (data.previousBalance || 0) - data.total;

            // 將資料編碼並傳送到預覽頁面
            const encodedData = encodeURIComponent(JSON.stringify(data));
            const previewUrl = \`/preview-invoice?data=\${encodedData}\`;

            // 在新視窗開啟預覽
            window.open(previewUrl, '_blank', 'width=800,height=1000,scrollbars=yes');
          }

          // 頁面載入時初始化
          document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            calculateTotal();
          });

          // 如果DOMContentLoaded已經觸發，直接執行初始化
          if (document.readyState === 'loading') {
            // 文檔還在載入中，等待DOMContentLoaded事件
          } else {
            // 文檔已經載入完成，直接執行初始化
            loadProducts();
            calculateTotal();
          }
        </script>
      </body>
    </html>
  `;

  return new Response(html, {
    status: 200,
    headers: {
      'Content-Type': 'text/html; charset=utf-8'
    }
  });
}

// 處理提交出貨記錄
async function handleSubmitInvoice(request, env) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    const data = await request.json();

    // 獲取檔案 ID
    const fileId = data.fileId;
    if (!fileId) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少檔案ID'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 獲取 access token，優先使用 API Key
    const accessToken = getAccessToken(request);

    try {
      // 準備要寫入的資料行
      const newRow = [
        data.invoiceDate,  // A欄：日期
        data.items.map(item => `${item.name} x${item.quantity}`).join(', '), // B欄：品名
        data.items.reduce((sum, item) => sum + item.quantity, 0), // C欄：總數量
        data.total,        // D欄：價錢
        data.total,        // E欄：小計
        data.balance       // F欄：剩餘金額
      ];

      let writeSuccess = false;

      // 先嘗試用 API Key 寫入
      if (env.GOOGLE_API_KEY && !writeSuccess) {
        try {
          const appendUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F:append?valueInputOption=USER_ENTERED&key=${env.GOOGLE_API_KEY}`;
          const response = await fetch(appendUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              range: 'A:F',
              majorDimension: 'ROWS',
              values: [newRow]
            })
          });

          if (response.ok) {
            writeSuccess = true;
            console.log('使用 API Key 成功寫入');
          } else {
            console.error('API Key 寫入失敗:', await response.text());
          }
        } catch (error) {
          console.error('API Key 寫入錯誤:', error);
        }
      }

      // 如果 API Key 失敗，嘗試用 OAuth
      if (accessToken && !writeSuccess) {
        try {
          const appendUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F:append?valueInputOption=USER_ENTERED`;
          const response = await fetch(appendUrl, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              range: 'A:F',
              majorDimension: 'ROWS',
              values: [newRow]
            })
          });

          if (response.ok) {
            writeSuccess = true;
            console.log('使用 OAuth 成功寫入');
          } else {
            console.error('OAuth 寫入失敗:', await response.text());
          }
        } catch (error) {
          console.error('OAuth 寫入錯誤:', error);
        }
      }

      if (writeSuccess) {
        return new Response(JSON.stringify({
          success: true,
          message: '出貨記錄已成功儲存到 Google Sheets',
          data: data
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else {
        return new Response(JSON.stringify({
          success: false,
          error: '無法寫入 Google Sheets，請檢查權限設定或進行 OAuth 認證'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }

    } catch (writeError) {
      console.error('寫入 Google Sheets 失敗:', writeError);
      return new Response(JSON.stringify({
        success: false,
        error: '寫入失敗: ' + writeError.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}

// 處理預覽出貨單
// 從cookie或header獲取access token
function getAccessToken(request) {
  // 先嘗試從Cookie獲取
  const cookieHeader = request.headers.get('Cookie');
  if (cookieHeader) {
    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {});
    if (cookies.access_token) {
      return cookies.access_token;
    }
  }

  // 也可以從Authorization header獲取
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.slice(7);
  }

  return null;
}

async function handleGetLastBalance(request, env) {
  try {
    const requestBody = await request.json();
    const { fileId, customerId } = requestBody;

    if (!fileId) {
      return new Response(JSON.stringify({ error: '未提供檔案ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 獲取access token
    const accessToken = getAccessToken(request);

    // 優先嘗試用 API Key（更可靠）
    if (env.GOOGLE_API_KEY) {
      try {
        const apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F?key=${env.GOOGLE_API_KEY}`;
        console.log('沒有 OAuth token，直接使用 API Key:', apiUrl);

        const response = await fetch(apiUrl, {
          headers: {
            'Accept': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          let lastBalance = 0;
          // 從最後一行開始尋找有餘額資料的行（餘額在F欄，索引為5）
          if (data.values && data.values.length > 0) {
            for (let i = data.values.length - 1; i >= 0; i--) {
              const row = data.values[i];
              if (row && row[5] && !isNaN(parseFloat(row[5]))) {
                lastBalance = parseFloat(row[5]);
                console.log('從API Key找到餘額（F欄）:', lastBalance);
                break;
              }
            }
          }
          return new Response(JSON.stringify({
            lastBalance,
            note: '使用API Key從Google Sheets讀取的資料'
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        } else {
          const errorText = await response.text();
          console.error('API Key 請求失敗:', response.status, errorText);
        }
      } catch (error) {
        console.error('API Key 存取失敗:', error);
      }
    }

    if (!accessToken) {
      // 如果沒有access token，返回模擬資料
      const mockBalancesByFileId = {
        '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ': 53455, // 百合藥局檔案
        '1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0': 24800, // 心芯藥局檔案
        '1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo': 15600, // 悅橙藥局檔案
        '1S-Qpj3U8DyY318wWC0xN0M7b31grDp0Kql6gMfDEnpo': 82300, // 幸運草藥局檔案
        '1O--Clvs-JjINkGISkU6e4MXFIBxEyZG1Cq5ol8W8SaM': 36700, // 繁華藥局檔案
        '1RxkKQUL7cDJFFR91ApNrNiAYKh6lQT2CeXA7HdD1ZcU': 19200, // 家欣藥局檔案
        '1F-yTFPnllUkIWuHURgHXV-E31fMuUC83kC6q5AXfaSc': 64500, // 西湖藥局檔案
        '1Q16-R1YuBhHO0pGe-_HHQM7VkBLYiyOT5BA6TeXDuCA': 41800, // 耀元藥局檔案
        '1rAiGha2Rp5-mNVXLOBrNQZW3MlE-8PKuMxx0A7wiKtE': 28900, // 愛維康藥局檔案
        '1g6Lj32CubziW3PuxKridRBJCEW0V6vppMfz60EhDHlI': 56100, // 景賀藥局檔案
        '1ZIbx7MtUiempMfp4NDxyoyLY3HE1LkSSfrFgQouju2I': 33400, // 辰鴻藥局檔案
        '1TwTsnGO5MXKhX9pK7nL9xQAVCndfGt4tksznFiLrt50': 48700, // 豐原福倫藥局檔案
        '166vUmqixAedk_ds9DzopISBI2pRfKBzVQu0eL4xTlxQ': 21600, // 北斗福倫藥局檔案
        '1s1Qem6dAOGp06SVxGI6_Q57YBS2WwMNBO0HjuxEzoy0': 37300, // 株一藥局檔案
        '1MPUu5CjHIPraccilf5AvKhLBLnJdmPEOINlsgABTdI4': 59800, // 嘉鶴藥局檔案
        '1Hatilpfnrcm2rn-8HsTOxiY-U56waMflbCzzHRuc1ak': 26500  // 雪仁藥局檔案
      };

      const lastBalance = mockBalancesByFileId[fileId] || 0;
      return new Response(JSON.stringify({
        lastBalance,
        note: '這是模擬資料。請先進行OAuth認證以讀取真實的Google Sheets資料。'
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 使用真實的Google Sheets API讀取資料（只有有 OAuth token 時才執行）
    if (accessToken) {
      try {
        // 先嘗試獲取檔案的metadata來檢查權限和工作表資訊
        const metadataUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}`;

        console.log('正在檢查檔案權限:', metadataUrl);
        console.log('使用Access Token:', accessToken.substring(0, 20) + '...');

        const metadataResponse = await fetch(metadataUrl, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Accept': 'application/json'
          }
        });

        if (!metadataResponse.ok) {
          const errorText = await metadataResponse.text();
          console.error('檔案metadata取得失敗:', errorText);

          // 直接嘗試讀取資料，不指定工作表名稱
          const fallbackUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:E`;
          console.log('嘗試fallback API:', fallbackUrl);
        } else {
          const metadata = await metadataResponse.json();
          console.log('檔案資訊:', {
            title: metadata.properties?.title,
            sheets: metadata.sheets?.map(s => s.properties?.title)
          });
        }

        // 嘗試多種可能的工作表名稱 - 讀取到F欄以包含餘額
        let apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F`;

        // 如果有metadata，使用第一個工作表的實際名稱
        if (metadataResponse.ok) {
          const metadata = await metadataResponse.json();
          if (metadata.sheets && metadata.sheets.length > 0) {
            const firstSheetName = metadata.sheets[0].properties.title;
            apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/'${firstSheetName}'!A:F`;
            console.log('使用工作表名稱:', firstSheetName);
          }
        }

        console.log('正在呼叫Google Sheets API:', apiUrl);

        // 先嘗試使用OAuth token
        let response = await fetch(apiUrl, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Accept': 'application/json'
          }
        });

        // 如果OAuth失敗，嘗試使用API Key
        if (!response.ok) {
          console.log('OAuth呼叫失敗，嘗試API Key方式');
          const apiKeyUrl = `${apiUrl}?key=${env.GOOGLE_API_KEY}`;
          console.log('使用API Key URL:', apiKeyUrl);

          response = await fetch(apiKeyUrl, {
            headers: {
              'Accept': 'application/json'
            }
          });

          // 如果API Key也失敗，嘗試公開CSV匯出（最後手段）
          if (!response.ok) {
            console.log('API Key也失敗，嘗試CSV匯出方式');
            const csvUrl = `https://docs.google.com/spreadsheets/d/${fileId}/export?format=csv`;
            console.log('嘗試CSV URL:', csvUrl);

            const csvResponse = await fetch(csvUrl);
            if (csvResponse.ok) {
              const csvText = await csvResponse.text();
              console.log('CSV資料長度:', csvText.length);

              // 簡單解析CSV找最後一筆餘額 - 餘額在F欄（索引5）
              const lines = csvText.split('\n').filter(line => line.trim());
              for (let i = lines.length - 1; i >= 0; i--) {
                const cols = lines[i].split(',');
                if (cols[5] && !isNaN(parseFloat(cols[5]))) {
                  const balance = parseFloat(cols[5]);
                  console.log('從CSV找到餘額（F欄）:', balance);
                  return new Response(JSON.stringify({
                    lastBalance: balance,
                    note: '從CSV匯出讀取的資料（F欄餘額）'
                  }), {
                    headers: { 'Content-Type': 'application/json' }
                  });
                }
              }
            }

            // 設定response為CSV響應以便後續統一處理
            response = csvResponse;
          }
        }

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Google Sheets API錯誤:', errorText);
          console.error('Response status:', response.status, response.statusText);

          // 根據錯誤類型提供不同的回應
          let errorNote = '';
          if (response.status === 401) {
            errorNote = '認證失效或未授權。請重新進行OAuth認證，目前使用模擬資料。';
          } else if (response.status === 403) {
            errorNote = '無權限存取此Google Sheets檔案。請確認：1) 檔案已分享給授權帳戶，或 2) 檔案設為「知道連結的使用者可檢視」';
          } else if (response.status === 404) {
            errorNote = '找不到指定的Google Sheets檔案。請檢查檔案ID是否正確';
          } else {
            errorNote = `API呼叫失敗：${response.status} ${response.statusText}`;
          }

          // 如果API失敗，回退到模擬資料
          const mockBalancesByFileId = {
            '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ': 53455,
            '1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0': 24800,
            '1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo': 15600
          };

          const lastBalance = mockBalancesByFileId[fileId] || 0;
          return new Response(JSON.stringify({
            lastBalance,
            note: errorNote,
            fallback: true
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }

        const data = await response.json();
        let lastBalance = 0;

        // 從最後一行開始尋找有餘額資料的行（餘額在F欄，索引為5）
        if (data.values && data.values.length > 0) {
          for (let i = data.values.length - 1; i >= 0; i--) {
            const row = data.values[i];
            if (row && row[5] && !isNaN(parseFloat(row[5]))) {
              lastBalance = parseFloat(row[5]);
              console.log('從API找到餘額（F欄）:', lastBalance);
              break;
            }
          }
        }

        return new Response(JSON.stringify({
          lastBalance,
          note: '從Google Sheets讀取的真實資料'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });

      } catch (apiError) {
        console.error('Google Sheets API呼叫錯誤:', apiError);

        // 如果API呼叫失敗，回退到模擬資料
        const mockBalancesByFileId = {
          '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ': 53455,
          '1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0': 24800,
          '1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo': 15600
        };

        const lastBalance = mockBalancesByFileId[fileId] || 0;
        return new Response(JSON.stringify({
          lastBalance,
          note: `API呼叫異常，使用模擬資料。錯誤: ${apiError.message}`
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

  } catch (error) {
    return new Response(JSON.stringify({ error: '處理請求時發生錯誤: ' + error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// 處理獲取商品清單API
async function handleGetProducts(request, env) {
  try {
    // 返回商品清單資料
    return new Response(JSON.stringify({
      success: true,
      products: productsData.products
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: '獲取商品清單時發生錯誤: ' + error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

async function handlePreviewInvoice(request, env) {
  try {
    const url = new URL(request.url);
    const encodedData = url.searchParams.get('data');

    if (!encodedData) {
      return new Response('缺少出貨單資料', { status: 400 });
    }

    const data = JSON.parse(decodeURIComponent(encodedData));

    // 生成出貨單 HTML
    const html = generateInvoiceHTML(data);

    return new Response(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });

  } catch (error) {
    return new Response('解析出貨單資料時發生錯誤: ' + error.message, { status: 500 });
  }
}

// 生成出貨單 HTML
function generateInvoiceHTML(data) {
  const invoiceNumber = 'INV' + new Date(data.invoiceDate).getFullYear() +
    String(new Date(data.invoiceDate).getMonth() + 1).padStart(2, '0') +
    String(new Date(data.invoiceDate).getDate()).padStart(2, '0') +
    String(Math.floor(Math.random() * 1000)).padStart(3, '0');

  // 根據公司 ID 設定供應商資訊
  let supplierInfo = {
    name: '您的公司名稱',
    phone: '06-2906741 / 0980347570',
    address: '台南市東區崇學路165號5樓'
  };

  if (data.companyId === 'liang-xin') {
    supplierInfo = {
      name: '量心醫藥股份有限公司',
      phone: '06-2906741 / 0980347570',
      address: '台南市東區崇學路165號5樓'
    };
  } else if (data.companyId === 'jia-xuan') {
    supplierInfo = {
      name: '嘉萱漢方有限公司',
      phone: '06-2906741 / 0980347570',
      address: '台南市東區崇學路165號5樓'
    };
  }

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>出貨單 - ${data.clientName}</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Microsoft YaHei', '微軟正黑體', Arial, sans-serif;
            background: #f5f7fa;
            padding: 20px;
            color: #333;
          }

          .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
          }

          .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
          }

          .invoice-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            letter-spacing: 2px;
          }

          .invoice-number {
            font-size: 14px;
            opacity: 0.9;
            position: absolute;
            top: 15px;
            right: 30px;
          }

          .invoice-body {
            padding: 40px;
          }

          .invoice-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 2px solid #f1f3f4;
          }

          .info-section h3 {
            color: #667eea;
            font-size: 16px;
            margin-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 5px;
          }

          .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
          }

          .info-label {
            color: #666;
            font-weight: 500;
          }

          .info-value {
            font-weight: 600;
            color: #333;
          }

          .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-radius: 8px;
            overflow: hidden;
          }

          .items-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            padding: 15px 20px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
          }

          .items-table td {
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
          }

          .items-table tbody tr:hover {
            background-color: #f8f9fa;
          }

          .items-table tbody tr:last-child td {
            border-bottom: none;
          }

          .text-right {
            text-align: right;
          }

          .text-center {
            text-align: center;
          }

          .amount {
            font-weight: 600;
            font-family: 'Courier New', monospace;
          }

          .summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
          }

          .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 16px;
          }

          .summary-row.total {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
            border-top: 2px solid #dee2e6;
            padding-top: 15px;
            margin-top: 20px;
            margin-bottom: 0;
          }

          .notes {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
          }

          .notes h4 {
            color: #856404;
            margin-bottom: 10px;
          }

          .notes p {
            color: #856404;
            line-height: 1.5;
            margin: 0;
          }

          .footer {
            text-align: center;
            color: #6c757d;
            font-size: 14px;
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
          }

          .actions {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
          }

          .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
          }

          .btn-primary {
            background: #667eea;
            color: white;
          }

          .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }

          .btn-secondary {
            background: #6c757d;
            color: white;
          }

          .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
          }

          @media print {
            body {
              background: white;
              padding: 0;
            }

            .invoice-container {
              box-shadow: none;
              border-radius: 0;
            }

            .actions, .btn {
              display: none !important;
            }

            .invoice-header {
              background: #667eea !important;
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }

            .summary {
              background: #f8f9fa !important;
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }
          }

          @media (max-width: 768px) {
            .invoice-info {
              grid-template-columns: 1fr;
              gap: 20px;
            }

            .items-table th,
            .items-table td {
              padding: 10px 15px;
            }

            .invoice-title {
              font-size: 24px;
            }

            .btn {
              display: block;
              margin: 10px auto;
              width: 200px;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="invoice-header">
            <div class="invoice-number">單號: ${invoiceNumber}</div>
            <div class="invoice-title">出 貨 單</div>
            <div>DELIVERY NOTE</div>
          </div>

          <div class="invoice-body">
            <div class="invoice-info">
              <div class="info-section">
                <h3>客戶資訊</h3>
                <div class="info-item">
                  <span class="info-label">客戶名稱：</span>
                  <span class="info-value">${data.clientName}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">出貨日期：</span>
                  <span class="info-value">${new Date(data.invoiceDate).toLocaleDateString('zh-TW')}</span>
                </div>
              </div>

              <div class="info-section">
                <h3>供應商資訊</h3>
                <div class="info-item">
                  <span class="info-label">公司名稱：</span>
                  <span class="info-value">${supplierInfo.name}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">聯絡電話：</span>
                  <span class="info-value">${supplierInfo.phone}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">地址：</span>
                  <span class="info-value">${supplierInfo.address}</span>
                </div>
              </div>
            </div>

            <table class="items-table">
              <thead>
                <tr>
                  <th style="width: 50px;">#</th>
                  <th>商品名稱</th>
                  <th class="text-center" style="width: 100px;">數量</th>
                  <th class="text-right" style="width: 120px;">單價</th>
                  <th class="text-right" style="width: 120px;">金額</th>
                </tr>
              </thead>
              <tbody>
                ${data.items.map((item, index) => `
                  <tr>
                    <td class="text-center">${index + 1}</td>
                    <td>${item.name}</td>
                    <td class="text-center">${item.quantity}</td>
                    <td class="text-right amount">$${item.unitPrice.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</td>
                    <td class="text-right amount">$${item.amount.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <div class="summary">
              <div class="summary-row">
                <span>商品小計：</span>
                <span class="amount">$${data.subtotal.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</span>
              </div>
              <div class="summary-row">
                <span>運費：</span>
                <span class="amount">$${data.shippingFee.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</span>
              </div>
              <div class="summary-row total">
                <span>本次應付總計：</span>
                <span class="amount">$${data.total.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</span>
              </div>
            </div>

            <div class="summary" style="background: #f0f8ff; border-left-color: #007bff;">
              <h4 style="color: #007bff; margin-bottom: 15px;">帳務計算</h4>
              <div class="summary-row">
                <span>前期餘額：</span>
                <span class="amount">$${(data.previousBalance || 0).toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</span>
              </div>
              <div class="summary-row">
                <span>減：本次應付：</span>
                <span class="amount">$${data.total.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</span>
              </div>
              <div class="summary-row total" style="background: ${data.balance === 0 ? '#d4edda' : data.balance < 0 ? '#f8d7da' : '#fff3cd'}; padding: 10px; border-radius: 5px;">
                <span style="color: ${data.balance === 0 ? '#155724' : data.balance < 0 ? '#721c24' : '#856404'};">
                  ${data.balance === 0 ? '✓ 已結清' : data.balance < 0 ? '客戶欠款：' : '客戶餘額：'}
                </span>
                <span class="amount" style="color: ${data.balance === 0 ? '#155724' : data.balance < 0 ? '#721c24' : '#856404'}; font-size: 22px;">
                  $${data.balance < 0 ? data.balance.toLocaleString('zh-TW', { minimumFractionDigits: 2 }) : (data.balance || 0).toLocaleString('zh-TW', { minimumFractionDigits: 2 })}
                </span>
              </div>
            </div>

            ${data.notes ? `
              <div class="notes">
                <h4>📝 備註</h4>
                <p>${data.notes}</p>
              </div>
            ` : ''}
          </div>

          <div class="actions">
            <button class="btn btn-primary" onclick="window.print()">🖨️ 列印出貨單</button>
            <button class="btn btn-secondary" onclick="window.close()">✕ 關閉視窗</button>
          </div>

          <div class="footer">
            <p>此出貨單由系統自動生成 | 生成時間：${new Date().toLocaleString('zh-TW')}</p>
          </div>
        </div>
      </body>
    </html>
  `;
}

// 生成包含嵌入式 Google Sheets 的 HTML 頁面
function getEmbeddedSheetPage(fileId, clientName) {
  // Google Sheets 嵌入式編輯器 URL - 使用編輯模式參數
  // rm=minimal 移除大部分 UI，widget=false 隱藏小工具，headers=false 隱藏標題
  const embedUrl = `https://docs.google.com/spreadsheets/d/${fileId}/edit?rm=minimal&headers=false&widget=false`;

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>${clientName ? clientName + ' - ' : ''}Google Sheets 編輯器</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
          }

          .header {
            background: #1a73e8;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }

          .header h1 {
            font-size: 20px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
          }

          .client-name {
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
          }

          .actions {
            display: flex;
            gap: 10px;
          }

          .btn {
            background: white;
            color: #1a73e8;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
          }

          .btn:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transform: translateY(-1px);
          }

          .iframe-container {
            flex: 1;
            position: relative;
            background: white;
            margin: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
          }

          iframe {
            width: 100%;
            height: 100%;
            border: none;
          }

          .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
          }

          .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1a73e8;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          @media (max-width: 768px) {
            .header {
              flex-direction: column;
              gap: 10px;
              text-align: center;
            }

            .iframe-container {
              margin: 10px;
            }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="8" y1="13" x2="16" y2="13"></line>
              <line x1="8" y1="17" x2="16" y2="17"></line>
            </svg>
            Google Sheets 編輯器
            ${clientName ? `<span class="client-name">${clientName}</span>` : ''}
          </h1>
          <div class="actions">
            <a href="https://docs.google.com/spreadsheets/d/${fileId}/edit" target="_blank" class="btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                <polyline points="15 3 21 3 21 9"></polyline>
                <line x1="10" y1="14" x2="21" y2="3"></line>
              </svg>
              在新視窗開啟
            </a>
            <button onclick="location.reload()" class="btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23 4 23 10 17 10"></polyline>
                <polyline points="1 20 1 14 7 14"></polyline>
                <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
              </svg>
              重新整理
            </button>
          </div>
        </div>

        <div class="iframe-container">
          <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在載入 Google Sheets...</p>
          </div>
          <iframe
            id="sheet-iframe"
            src="${embedUrl}"
            onload="document.getElementById('loading').style.display='none'"
            onerror="handleError()"
            allowfullscreen>
          </iframe>
        </div>

        <script>
          function handleError() {
            document.getElementById('loading').innerHTML =
              '<p style="color: #d93025;">無法載入 Google Sheets</p>' +
              '<p style="margin-top: 10px; font-size: 14px;">請確認您有存取權限</p>' +
              '<button onclick="location.reload()" class="btn" style="margin-top: 20px;">重試</button>';
          }

          // 超時處理
          setTimeout(function() {
            const loading = document.getElementById('loading');
            if (loading && loading.style.display !== 'none') {
              loading.innerHTML =
                '<p style="color: #ff9800;">載入時間較長...</p>' +
                '<p style="margin-top: 10px; font-size: 14px;">如果無法載入，請檢查您的網路連線</p>';
            }
          }, 10000);
        </script>
      </body>
    </html>
  `;
}