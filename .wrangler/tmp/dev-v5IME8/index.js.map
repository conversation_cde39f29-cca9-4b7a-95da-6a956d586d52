{"version": 3, "sources": ["../../../src/data/customers.json", "../../../src/index.js", "../../../../../../../../../../../opt/homebrew/lib/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../../../../../../../../../opt/homebrew/lib/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-CMHBbr/middleware-insertion-facade.js", "../../../../../../../../../../../opt/homebrew/lib/node_modules/wrangler/templates/middleware/common.ts", "../bundle-CMHBbr/middleware-loader.entry.ts"], "sourceRoot": "/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/.wrangler/tmp/dev-v5IME8", "sourcesContent": ["{\n  \"companies\": [\n    {\n      \"id\": \"liang-xin\",\n      \"name\": \"量心\",\n      \"clients\": [\n        {\n          \"id\": \"lx-client1\",\n          \"name\": \"百合藥局\"\n        },\n        {\n          \"id\": \"lx-client2\",\n          \"name\": \"心芯藥局\"\n        }\n      ]\n    },\n    {\n      \"id\": \"jia-xuan\",\n      \"name\": \"嘉萱\",\n      \"clients\": [\n        {\n          \"id\": \"jx-client1\",\n          \"name\": \"悅橙藥局\"\n        },\n        {\n          \"id\": \"jx-client2\",\n          \"name\": \"幸運草藥局\"\n        },\n        {\n          \"id\": \"jx-client3\",\n          \"name\": \"繁華藥局\"\n        },\n        {\n          \"id\": \"jx-client4\",\n          \"name\": \"家欣藥局\"\n        },\n        {\n          \"id\": \"jx-client5\",\n          \"name\": \"西湖藥局\"\n        },\n        {\n          \"id\": \"jx-client6\",\n          \"name\": \"耀元藥局\"\n        },\n        {\n          \"id\": \"jx-client7\",\n          \"name\": \"愛維康藥局\"\n        },\n        {\n          \"id\": \"jx-client8\",\n          \"name\": \"景賀藥局\"\n        },\n        {\n          \"id\": \"jx-client9\",\n          \"name\": \"辰鴻藥局\"\n        },\n        {\n          \"id\": \"jx-client10\",\n          \"name\": \"豐原福倫藥局\"\n        },\n        {\n          \"id\": \"jx-client11\",\n          \"name\": \"北斗福倫藥局\"\n        },\n        {\n          \"id\": \"jx-client12\",\n          \"name\": \"株一藥局\"\n        },\n        {\n          \"id\": \"jx-client13\",\n          \"name\": \"嘉鶴藥局\"\n        },\n        {\n          \"id\": \"jx-client14\",\n          \"name\": \"雪仁藥局\"\n        }\n      ]\n    }\n  ]\n}\n", "/**\n * Google Drive Excel 提取器\n *\n * 這個Cloudflare Worker允許用戶連接到Google Drive\n * 並提取Excel文件中的數據\n */\n\nimport customersData from './data/customers.json';\n\n// 處理HTTP請求的主要入口點\nexport default {\n  async fetch(request, env, ctx) {\n    const url = new URL(request.url);\n    const path = url.pathname;\n\n    // 主頁 - 顯示登入按鈕\n    if (path === \"/\" || path === \"\") {\n      return new Response(getHomePage(), {\n        headers: { \"Content-Type\": \"text/html\" },\n      });\n    }\n\n    // 處理OAuth回調\n    if (path === \"/oauth/callback\") {\n      return handleOAuthCallback(request, env);\n    }\n\n    // 處理認證，生成Google Drive授權URL\n    if (path === \"/auth\") {\n      return handleAuth(env);\n    }\n\n    // 列出Google Drive中的Excel文件\n    if (path === \"/list-files\") {\n      return handleListFiles(request, env);\n    }\n\n    // 提取Excel文件數據\n    if (path === \"/extract\") {\n      return handleExtractExcel(request, env);\n    }\n\n    // 客戶表單頁面\n    if (path === \"/customer-form\") {\n      return handleCustomerForm(request, env);\n    }\n\n    // 創建出貨記錄頁面\n    if (path === \"/create-invoice\") {\n      return handleCreateInvoice(request, env);\n    }\n\n    // 提交出貨記錄\n    if (path === \"/submit-invoice\") {\n      return handleSubmitInvoice(request, env);\n    }\n\n    // 預覽出貨單\n    if (path === \"/preview-invoice\") {\n      return handlePreviewInvoice(request, env);\n    }\n\n    // API: 獲取最後一筆餘額\n    if (path === \"/api/get-last-balance\") {\n      return handleGetLastBalance(request, env);\n    }\n\n    // 所有其他路徑返回404\n    return new Response(\"Not found\", { status: 404 });\n  },\n};\n\n// 返回簡單的HTML主頁，包含登入按鈕\nfunction getHomePage() {\n  return `\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>Google Drive Excel 提取器</title>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <style>\n          body {\n            font-family: Arial, sans-serif;\n            max-width: 800px;\n            margin: 0 auto;\n            padding: 20px;\n            line-height: 1.6;\n          }\n          button {\n            background-color: #4285f4;\n            color: white;\n            border: none;\n            padding: 10px 20px;\n            border-radius: 4px;\n            cursor: pointer;\n            font-size: 16px;\n          }\n          button:hover {\n            background-color: #3367d6;\n          }\n          .container {\n            margin-top: 50px;\n            text-align: center;\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <h1>Google Drive Excel 提取器</h1>\n          <p>連接到您的Google Drive並提取Excel文件數據</p>\n          <button onclick=\"window.location.href='/auth'\">使用Google登入</button>\n        </div>\n      </body>\n    </html>\n  `;\n}\n\n// 處理認證並重定向到Google的OAuth頁面\nasync function handleAuth(env) {\n  // 這裡將實現OAuth認證流程\n  // 目前是一個簡單的佔位符\n  return new Response(\"認證功能尚未實現\", { status: 501 });\n}\n\n// 處理從Google重定向回來的OAuth回調\nasync function handleOAuthCallback(request, env) {\n  // 這裡將處理OAuth回調\n  // 目前是一個簡單的佔位符\n  return new Response(\"OAuth回調功能尚未實現\", { status: 501 });\n}\n\n// 列出用戶Google Drive中的Excel文件\nasync function handleListFiles(request, env) {\n  // 這裡將列出Google Drive中的文件\n  // 目前是一個簡單的佔位符\n  return new Response(\"列出文件功能尚未實現\", { status: 501 });\n}\n\n// 從指定的Excel文件中提取數據並顯示嵌入式編輯器\nasync function handleExtractExcel(request, env) {\n  try {\n    const url = new URL(request.url);\n    const fileId = url.searchParams.get('fileId');\n    const clientName = url.searchParams.get('clientName');\n\n    if (!fileId) {\n      return new Response(JSON.stringify({ error: '缺少 fileId 參數' }), {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' }\n      });\n    }\n\n    // 返回包含嵌入式 Google Sheets 的 HTML 頁面\n    const html = getEmbeddedSheetPage(fileId, clientName);\n\n    return new Response(html, {\n      status: 200,\n      headers: {\n        'Content-Type': 'text/html; charset=utf-8'\n      }\n    });\n\n  } catch (error) {\n    console.error('顯示Excel編輯器時發生錯誤:', error);\n    return new Response(JSON.stringify({\n      error: '顯示Excel編輯器時發生錯誤',\n      details: error.message\n    }), {\n      status: 500,\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n}\n\n// 處理客戶表單頁面\nasync function handleCustomerForm(request, env) {\n  // 預設的 Google Sheets 檔案 ID（可以從環境變數讀取）\n  const defaultFileId = env.DEFAULT_FILE_ID || '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ';\n\n  // 從 JSON 檔案讀取所有客戶\n  const allCustomers = [];\n  customersData.companies.forEach(company => {\n    company.clients.forEach(client => {\n      allCustomers.push({\n        company: company.name,\n        name: client.name,\n        id: client.id\n      });\n    });\n  });\n\n  const html = `\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>選擇客戶 - 出貨單系統</title>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            min-height: 100vh;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            padding: 20px;\n          }\n\n          .container {\n            background: white;\n            border-radius: 20px;\n            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n            padding: 40px;\n            max-width: 500px;\n            width: 100%;\n          }\n\n          h1 {\n            color: #333;\n            text-align: center;\n            margin-bottom: 10px;\n            font-size: 28px;\n          }\n\n          .subtitle {\n            text-align: center;\n            color: #666;\n            margin-bottom: 30px;\n            font-size: 14px;\n          }\n\n          .form-group {\n            margin-bottom: 25px;\n          }\n\n          label {\n            display: block;\n            color: #555;\n            font-weight: 500;\n            margin-bottom: 10px;\n            font-size: 14px;\n          }\n\n          select, input {\n            width: 100%;\n            padding: 12px 15px;\n            border: 2px solid #e0e0e0;\n            border-radius: 10px;\n            font-size: 16px;\n            transition: all 0.3s ease;\n            background: white;\n          }\n\n          select:focus, input:focus {\n            outline: none;\n            border-color: #667eea;\n            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n          }\n\n          .company-section {\n            margin-bottom: 25px;\n          }\n\n          .company-title {\n            color: #555;\n            font-size: 16px;\n            font-weight: 600;\n            margin-bottom: 10px;\n            padding-left: 5px;\n            border-left: 3px solid #667eea;\n          }\n\n          .customer-list {\n            display: grid;\n            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n            gap: 15px;\n            margin-top: 15px;\n          }\n\n          .customer-card {\n            background: white;\n            border: 2px solid #e1e5e9;\n            border-radius: 12px;\n            padding: 20px;\n            transition: all 0.3s ease;\n          }\n\n          .customer-card:hover {\n            border-color: #667eea;\n            transform: translateY(-2px);\n            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);\n          }\n\n          .customer-name {\n            font-size: 18px;\n            font-weight: 600;\n            color: #333;\n            margin-bottom: 15px;\n            text-align: center;\n          }\n\n          .customer-actions {\n            display: flex;\n            gap: 8px;\n          }\n\n          .btn-action {\n            flex: 1;\n            padding: 10px 15px;\n            border: none;\n            border-radius: 8px;\n            cursor: pointer;\n            font-size: 14px;\n            font-weight: 500;\n            text-decoration: none;\n            text-align: center;\n            transition: all 0.3s ease;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: 5px;\n          }\n\n          .btn-view {\n            background: #f8f9fa;\n            color: #495057;\n            border: 1px solid #dee2e6;\n          }\n\n          .btn-view:hover {\n            background: #e9ecef;\n            transform: translateY(-1px);\n          }\n\n          .btn-create {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n          }\n\n          .btn-create:hover {\n            transform: translateY(-1px);\n            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n          }\n\n          .submit-button {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            border: none;\n            padding: 15px 30px;\n            border-radius: 10px;\n            cursor: pointer;\n            font-size: 18px;\n            font-weight: 600;\n            width: 100%;\n            transition: all 0.3s ease;\n            margin-top: 20px;\n          }\n\n          .submit-button:hover:not(:disabled) {\n            transform: translateY(-2px);\n            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n          }\n\n          .submit-button:disabled {\n            opacity: 0.5;\n            cursor: not-allowed;\n          }\n\n          .divider {\n            text-align: center;\n            color: #999;\n            margin: 30px 0;\n            position: relative;\n          }\n\n          .divider::before {\n            content: '';\n            position: absolute;\n            top: 50%;\n            left: 0;\n            right: 0;\n            height: 1px;\n            background: #e0e0e0;\n          }\n\n          .divider span {\n            background: white;\n            padding: 0 15px;\n            position: relative;\n          }\n\n          .custom-input-group {\n            display: flex;\n            gap: 10px;\n            align-items: flex-end;\n          }\n\n          .custom-input-group input {\n            flex: 1;\n          }\n\n          .custom-input-group button {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            border: none;\n            padding: 12px 20px;\n            border-radius: 10px;\n            cursor: pointer;\n            font-size: 16px;\n            font-weight: 500;\n            transition: all 0.3s ease;\n            white-space: nowrap;\n          }\n\n          .custom-input-group button:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);\n          }\n\n          @media (max-width: 480px) {\n            .container {\n              padding: 30px 20px;\n            }\n\n            h1 {\n              font-size: 24px;\n            }\n\n            .customer-list {\n              grid-template-columns: 1fr;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <h1>📋 出貨單系統</h1>\n          <p class=\"subtitle\">選擇客戶以開啟對應的出貨單</p>\n\n          <div class=\"form-group\">\n            <label for=\"fileId\">Google Sheets 檔案 ID</label>\n            <input\n              type=\"text\"\n              id=\"fileId\"\n              name=\"fileId\"\n              value=\"${defaultFileId}\"\n              placeholder=\"輸入 Google Sheets 檔案 ID\"\n            />\n          </div>\n\n          <div class=\"divider\">\n            <span>快速選擇客戶</span>\n          </div>\n\n          ${customersData.companies.map(company => `\n            <div class=\"company-section\">\n              <h3 class=\"company-title\">${company.name}</h3>\n              <div class=\"customer-list\">\n                ${company.clients.map(client => `\n                  <div class=\"customer-card\">\n                    <div class=\"customer-name\">${client.name}</div>\n                    <div class=\"customer-actions\">\n                      <a href=\"/extract?fileId=${defaultFileId}&clientName=${encodeURIComponent(client.name)}\"\n                         class=\"btn-action btn-view\"\n                         onclick=\"updateLink(event, this)\">\n                        📊 查看記錄\n                      </a>\n                      <a href=\"/create-invoice?fileId=${defaultFileId}&clientName=${encodeURIComponent(client.name)}\"\n                         class=\"btn-action btn-create\"\n                         onclick=\"updateInvoiceLink(event, this)\">\n                        📝 建立出貨單\n                      </a>\n                    </div>\n                  </div>\n                `).join('')}\n              </div>\n            </div>\n          `).join('')}\n\n          <div class=\"divider\">\n            <span>或輸入自訂客戶名稱</span>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"customClient\">自訂客戶名稱</label>\n            <div class=\"custom-input-group\">\n              <input\n                type=\"text\"\n                id=\"customClient\"\n                name=\"customClient\"\n                placeholder=\"輸入客戶名稱\"\n              />\n              <button onclick=\"goToCustomClient()\">開啟</button>\n            </div>\n          </div>\n        </div>\n\n        <script>\n          // 更新連結以使用當前的檔案 ID\n          function updateLink(event, element) {\n            event.preventDefault();\n            const fileId = document.getElementById('fileId').value;\n            const url = new URL(element.href);\n            url.searchParams.set('fileId', fileId);\n            window.location.href = url.toString();\n          }\n\n          // 更新出貨單連結以使用當前的檔案 ID\n          function updateInvoiceLink(event, element) {\n            event.preventDefault();\n            const fileId = document.getElementById('fileId').value;\n            const url = new URL(element.href);\n            url.searchParams.set('fileId', fileId);\n            window.location.href = url.toString();\n          }\n\n          // 前往自訂客戶\n          function goToCustomClient() {\n            const fileId = document.getElementById('fileId').value;\n            const clientName = document.getElementById('customClient').value;\n\n            if (!clientName.trim()) {\n              alert('請輸入客戶名稱');\n              return;\n            }\n\n            window.location.href = \\`/extract?fileId=\\${encodeURIComponent(fileId)}&clientName=\\${encodeURIComponent(clientName)}\\`;\n          }\n\n          // Enter 鍵送出\n          document.getElementById('customClient').addEventListener('keypress', function(e) {\n            if (e.key === 'Enter') {\n              goToCustomClient();\n            }\n          });\n\n          // 儲存檔案 ID 到 localStorage\n          document.getElementById('fileId').addEventListener('change', function() {\n            localStorage.setItem('defaultFileId', this.value);\n          });\n\n          // 從 localStorage 載入檔案 ID\n          window.addEventListener('load', function() {\n            const savedFileId = localStorage.getItem('defaultFileId');\n            if (savedFileId) {\n              document.getElementById('fileId').value = savedFileId;\n            }\n          });\n        </script>\n      </body>\n    </html>\n  `;\n\n  return new Response(html, {\n    status: 200,\n    headers: {\n      'Content-Type': 'text/html; charset=utf-8'\n    }\n  });\n}\n\n// 處理創建出貨記錄頁面\nasync function handleCreateInvoice(request, env) {\n  const url = new URL(request.url);\n  const clientName = url.searchParams.get('clientName') || '';\n  const fileId = url.searchParams.get('fileId') || env.DEFAULT_FILE_ID || '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ';\n\n  // 根據客戶名稱找出所屬公司和客戶ID\n  let companyId = '';\n  let companyName = '';\n  let customerId = '';\n\n  for (const company of customersData.companies) {\n    const client = company.clients.find(c => c.name === clientName);\n    if (client) {\n      companyId = company.id;\n      companyName = company.name;\n      customerId = client.id;\n      break;\n    }\n  }\n\n  const html = `\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>創建出貨記錄 - ${clientName}</title>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            background: #f5f7fa;\n            min-height: 100vh;\n            padding: 20px;\n          }\n\n          .container {\n            max-width: 800px;\n            margin: 0 auto;\n            background: white;\n            border-radius: 12px;\n            box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n            overflow: hidden;\n          }\n\n          .header {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            padding: 20px;\n            text-align: center;\n          }\n\n          .form-section {\n            padding: 30px;\n          }\n\n          .form-group {\n            margin-bottom: 20px;\n          }\n\n          label {\n            display: block;\n            color: #555;\n            font-weight: 500;\n            margin-bottom: 8px;\n          }\n\n          input, select, textarea {\n            width: 100%;\n            padding: 12px;\n            border: 2px solid #e1e5e9;\n            border-radius: 8px;\n            font-size: 16px;\n            transition: border-color 0.3s;\n          }\n\n          input:focus, select:focus, textarea:focus {\n            outline: none;\n            border-color: #667eea;\n          }\n\n          .row {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 15px;\n          }\n\n          .items-section {\n            border-top: 1px solid #e1e5e9;\n            padding-top: 20px;\n            margin-top: 20px;\n          }\n\n          .item-row {\n            display: grid;\n            grid-template-columns: 2fr 1fr 1fr 1fr auto;\n            gap: 10px;\n            align-items: end;\n            margin-bottom: 10px;\n            padding: 10px;\n            background: #f8f9fa;\n            border-radius: 8px;\n          }\n\n          .btn {\n            padding: 12px 20px;\n            border: none;\n            border-radius: 8px;\n            cursor: pointer;\n            font-weight: 500;\n            transition: all 0.3s;\n          }\n\n          .btn-primary {\n            background: #667eea;\n            color: white;\n          }\n\n          .btn-success {\n            background: #28a745;\n            color: white;\n          }\n\n          .btn-danger {\n            background: #dc3545;\n            color: white;\n          }\n\n          .btn-secondary {\n            background: #6c757d;\n            color: white;\n          }\n\n          .btn:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n          }\n\n          .summary {\n            background: #f8f9fa;\n            padding: 20px;\n            border-radius: 8px;\n            margin-top: 20px;\n          }\n\n          .summary-row {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 10px;\n          }\n\n          .total {\n            font-size: 18px;\n            font-weight: bold;\n            color: #333;\n            border-top: 2px solid #667eea;\n            padding-top: 10px;\n          }\n\n          @media (max-width: 768px) {\n            .row {\n              grid-template-columns: 1fr;\n            }\n\n            .item-row {\n              grid-template-columns: 1fr;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <h1>📋 創建出貨記錄</h1>\n            <p>客戶：${clientName} (${companyName})</p>\n          </div>\n\n          <div class=\"form-section\">\n            <form id=\"invoiceForm\">\n              <input type=\"hidden\" name=\"fileId\" value=\"${fileId}\">\n              <input type=\"hidden\" name=\"clientName\" value=\"${clientName}\">\n              <input type=\"hidden\" name=\"companyId\" value=\"${companyId}\">\n              <input type=\"hidden\" name=\"companyName\" value=\"${companyName}\">\n\n              <div class=\"row\">\n                <div class=\"form-group\">\n                  <label for=\"invoiceDate\">出貨日期</label>\n                  <input type=\"date\" id=\"invoiceDate\" name=\"invoiceDate\" required>\n                </div>\n                <div class=\"form-group\">\n                  <label for=\"shippingFee\">郵費</label>\n                  <input type=\"number\" id=\"shippingFee\" name=\"shippingFee\" min=\"0\" value=\"0\" onchange=\"calculateTotal()\">\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"form-group\">\n                  <label for=\"previousBalance\">前期餘額</label>\n                  <div style=\"display: flex; gap: 10px; align-items: center;\">\n                    <input type=\"number\" id=\"previousBalance\" name=\"previousBalance\" step=\"0.01\" value=\"0\" onchange=\"calculateTotal()\" readonly style=\"flex: 1;\">\n                    <button type=\"button\" onclick=\"fetchLastBalance()\" style=\"padding: 8px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">從Excel抓取</button>\n                  </div>\n                </div>\n                <div class=\"form-group\">\n                  <label for=\"paidAmount\">本次餘額</label>\n                  <input type=\"number\" id=\"paidAmount\" name=\"paidAmount\" min=\"0\" step=\"0.01\" value=\"0\" onchange=\"calculateTotal()\">\n                </div>\n              </div>\n\n              <div class=\"items-section\">\n                <h3>商品明細</h3>\n                <div id=\"itemsList\">\n                  <div class=\"item-row\">\n                    <div>\n                      <label>商品名稱</label>\n                      <input type=\"text\" name=\"itemName[]\" placeholder=\"輸入商品名稱\" required onchange=\"calculateTotal()\">\n                    </div>\n                    <div>\n                      <label>數量</label>\n                      <input type=\"number\" name=\"quantity[]\" min=\"1\" value=\"1\" required onchange=\"calculateTotal()\">\n                    </div>\n                    <div>\n                      <label>單價</label>\n                      <input type=\"number\" name=\"unitPrice[]\" min=\"0\" step=\"0.01\" required onchange=\"calculateTotal()\">\n                    </div>\n                    <div>\n                      <label>金額</label>\n                      <input type=\"number\" name=\"amount[]\" readonly>\n                    </div>\n                    <div>\n                      <label>&nbsp;</label>\n                      <button type=\"button\" class=\"btn btn-danger\" onclick=\"removeItem(this)\">刪除</button>\n                    </div>\n                  </div>\n                </div>\n\n                <button type=\"button\" class=\"btn btn-secondary\" onclick=\"addItem()\">+ 新增商品</button>\n              </div>\n\n              <div class=\"form-group\">\n                <label for=\"notes\">備註</label>\n                <textarea id=\"notes\" name=\"notes\" rows=\"3\" placeholder=\"輸入備註資訊\"></textarea>\n              </div>\n\n              <div class=\"summary\">\n                <div class=\"summary-row\">\n                  <span>商品小計：</span>\n                  <span id=\"subtotal\">$0</span>\n                </div>\n                <div class=\"summary-row\">\n                  <span>郵費：</span>\n                  <span id=\"shippingDisplay\">$0</span>\n                </div>\n                <div class=\"summary-row total\">\n                  <span>本次應付總計：</span>\n                  <span id=\"total\">$0</span>\n                </div>\n                <div style=\"margin: 20px 0; border-top: 1px dashed #dee2e6;\"></div>\n                <div class=\"summary-row\">\n                  <span>前期餘額：</span>\n                  <span id=\"previousBalanceDisplay\">$0</span>\n                </div>\n                <div class=\"summary-row\">\n                  <span>加：本次應付：</span>\n                  <span id=\"currentTotalDisplay\">$0</span>\n                </div>\n                <div class=\"summary-row\">\n                  <span>本次餘額：</span>\n                  <span id=\"paidAmountDisplay\">$0</span>\n                </div>\n                <div class=\"summary-row total\" style=\"background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;\">\n                  <span>餘額（客戶欠款）：</span>\n                  <span id=\"balance\">$0</span>\n                </div>\n              </div>\n\n              <div style=\"margin-top: 30px; text-align: center;\">\n                <button type=\"submit\" class=\"btn btn-success\" style=\"margin-right: 10px;\">💾 儲存記錄</button>\n                <button type=\"button\" class=\"btn btn-primary\" onclick=\"previewInvoice()\">👁️ 預覽出貨單</button>\n              </div>\n            </form>\n          </div>\n        </div>\n\n        <script>\n          // 設定今天的日期為預設值\n          document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];\n\n          function addItem() {\n            const itemsList = document.getElementById('itemsList');\n            const newItem = document.createElement('div');\n            newItem.className = 'item-row';\n            newItem.innerHTML = \\`\n              <div>\n                <label>商品名稱</label>\n                <input type=\"text\" name=\"itemName[]\" placeholder=\"輸入商品名稱\" required onchange=\"calculateTotal()\">\n              </div>\n              <div>\n                <label>數量</label>\n                <input type=\"number\" name=\"quantity[]\" min=\"1\" value=\"1\" required onchange=\"calculateTotal()\">\n              </div>\n              <div>\n                <label>單價</label>\n                <input type=\"number\" name=\"unitPrice[]\" min=\"0\" step=\"0.01\" required onchange=\"calculateTotal()\">\n              </div>\n              <div>\n                <label>金額</label>\n                <input type=\"number\" name=\"amount[]\" readonly>\n              </div>\n              <div>\n                <label>&nbsp;</label>\n                <button type=\"button\" class=\"btn btn-danger\" onclick=\"removeItem(this)\">刪除</button>\n              </div>\n            \\`;\n            itemsList.appendChild(newItem);\n          }\n\n          function removeItem(button) {\n            if (document.querySelectorAll('.item-row').length > 1) {\n              button.closest('.item-row').remove();\n              calculateTotal();\n            }\n          }\n\n          async function fetchLastBalance() {\n            const customerId = '${customerId}';\n            const fileId = getFileIdForCustomer(customerId);\n\n            if (!fileId) {\n              alert('找不到對應的Excel檔案ID');\n              return;\n            }\n\n            try {\n              // 調用後端API來抓取最後一筆餘額\n              const response = await fetch('/api/get-last-balance', {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                  fileId: fileId,\n                  customerId: customerId\n                })\n              });\n\n              if (response.ok) {\n                const result = await response.json();\n                document.getElementById('previousBalance').value = result.lastBalance || 0;\n                calculateTotal();\n              } else {\n                alert('抓取餘額失敗: ' + response.statusText);\n              }\n            } catch (error) {\n              alert('抓取餘額時發生錯誤: ' + error.message);\n            }\n          }\n\n          function getFileIdForCustomer(customerId) {\n            // 每個客戶對應自己的Google Sheets檔案ID\n            // 實際使用時需要設定各客戶的正確檔案ID\n            const fileMapping = {\n              'lx-client1': '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ', // 百合藥局\n              'lx-client2': '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms', // 心芯藥局\n              'jx-client1': '1ZFGJKLmn9oPQRstuVWXyz1a2b3c4d5e6f7g8h9i0j', // 悅橙藥局\n              'jx-client2': '1ABCDEFGHijklMNOPqrsTUvwXYZ1234567890abcde', // 幸運草藥局\n              'jx-client3': '1QWERTYUIOPasdfGHJKLzxcvBNM1234567890qwert', // 繁華藥局\n              'jx-client4': '1ASDFGHjklQWERTYzxcvBNMuioPASDF1234567890', // 家欣藥局\n              'jx-client5': '1ZXCVBNMqwerTYUIoPASDfghJKLzxcVBN1234567890', // 西湖藥局\n              'jx-client6': '1POIUYTREWqasdFGHJklZXCvbnm1234567890POIUY', // 耀元藥局\n              'jx-client7': '1MNBVCXzasdQWErtYUIOpasFGHjkl1234567890MNB', // 愛維康藥局\n              'jx-client8': '1LKJHGFdsaZXCvbnMQWERtyuioP1234567890LKJHG', // 景賀藥局\n              'jx-client9': '1QAZwsxEDCrfvTGByhnUJMikOLp1234567890QAZws', // 辰鴻藥局\n              'jx-client10': '1PLMoknIJBuhVYgcTFrdXEswZaq1234567890PLMok', // 豐原福倫藥局\n              'jx-client11': '1OKMijnHubGYvtfCRdxEsw2za1234567890OKMij', // 北斗福倫藥局\n              'jx-client12': '1NJMuhbGYvtfCRdxesw2ZAq13579024681NJMuh', // 株一藥局\n              'jx-client13': '1MKLoinHubGYvtfCRdxesw2ZA1357902468MKLoi', // 嘉鶴藥局\n              'jx-client14': '1LJKhubGYvtfCRdxesw2ZAq135790246810LJKh'  // 雪仁藥局\n            };\n            return fileMapping[customerId];\n          }\n\n          function calculateTotal() {\n            let subtotal = 0;\n            const itemRows = document.querySelectorAll('.item-row');\n\n            itemRows.forEach(row => {\n              const quantity = parseFloat(row.querySelector('input[name=\"quantity[]\"]').value) || 0;\n              const unitPrice = parseFloat(row.querySelector('input[name=\"unitPrice[]\"]').value) || 0;\n              const amount = quantity * unitPrice;\n\n              row.querySelector('input[name=\"amount[]\"]').value = amount.toFixed(2);\n              subtotal += amount;\n            });\n\n            const shippingFee = parseFloat(document.getElementById('shippingFee').value) || 0;\n            const total = subtotal + shippingFee;\n            const previousBalance = parseFloat(document.getElementById('previousBalance').value) || 0;\n            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;\n\n            // 餘額計算：本次餘額直接輸入\n            const balance = paidAmount;\n\n            // 更新顯示\n            document.getElementById('subtotal').textContent = '$' + subtotal.toFixed(2);\n            document.getElementById('shippingDisplay').textContent = '$' + shippingFee.toFixed(2);\n            document.getElementById('total').textContent = '$' + total.toFixed(2);\n            document.getElementById('previousBalanceDisplay').textContent = '$' + previousBalance.toFixed(2);\n            document.getElementById('currentTotalDisplay').textContent = '$' + total.toFixed(2);\n            document.getElementById('paidAmountDisplay').textContent = '$' + paidAmount.toFixed(2);\n            document.getElementById('balance').textContent = '$' + balance.toFixed(2);\n\n            // 餘額顏色提示\n            const balanceElement = document.getElementById('balance');\n            if (balance < 0) {\n              balanceElement.style.color = '#dc3545'; // 紅色表示客戶欠款\n            } else if (balance === 0) {\n              balanceElement.style.color = '#28a745'; // 綠色表示已結清\n            } else {\n              balanceElement.style.color = '#856404'; // 黃色表示客戶有餘額\n            }\n          }\n\n          document.getElementById('invoiceForm').addEventListener('submit', function(e) {\n            e.preventDefault();\n\n            // 收集表單資料\n            const formData = new FormData(this);\n            const data = {};\n\n            // 基本資料\n            data.fileId = formData.get('fileId');\n            data.clientName = formData.get('clientName');\n            data.companyId = formData.get('companyId');\n            data.companyName = formData.get('companyName');\n            data.invoiceDate = formData.get('invoiceDate');\n            data.shippingFee = parseFloat(formData.get('shippingFee')) || 0;\n            data.previousBalance = parseFloat(formData.get('previousBalance')) || 0;\n            data.paidAmount = parseFloat(formData.get('paidAmount')) || 0;\n            data.notes = formData.get('notes');\n\n            // 商品資料\n            data.items = [];\n            const itemNames = formData.getAll('itemName[]');\n            const quantities = formData.getAll('quantity[]');\n            const unitPrices = formData.getAll('unitPrice[]');\n\n            for (let i = 0; i < itemNames.length; i++) {\n              if (itemNames[i].trim()) {\n                data.items.push({\n                  name: itemNames[i],\n                  quantity: parseFloat(quantities[i]),\n                  unitPrice: parseFloat(unitPrices[i]),\n                  amount: parseFloat(quantities[i]) * parseFloat(unitPrices[i])\n                });\n              }\n            }\n\n            // 計算總計和餘額\n            data.subtotal = data.items.reduce((sum, item) => sum + item.amount, 0);\n            data.total = data.subtotal + data.shippingFee;\n            data.balance = data.paidAmount;\n\n            // 提交資料\n            fetch('/submit-invoice', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify(data)\n            })\n            .then(response => response.json())\n            .then(result => {\n              if (result.success) {\n                alert('出貨記錄已成功儲存！');\n                // 可以重新導向或清空表單\n                window.location.href = '/customer-form';\n              } else {\n                alert('儲存失敗：' + result.error);\n              }\n            })\n            .catch(error => {\n              alert('提交時發生錯誤：' + error.message);\n            });\n          });\n\n          function previewInvoice() {\n            // 收集表單資料並開啟預覽\n            const form = document.getElementById('invoiceForm');\n            const formData = new FormData(form);\n            const data = {};\n\n            // 基本資料\n            data.fileId = formData.get('fileId');\n            data.clientName = formData.get('clientName');\n            data.companyId = formData.get('companyId');\n            data.companyName = formData.get('companyName');\n            data.invoiceDate = formData.get('invoiceDate');\n            data.shippingFee = parseFloat(formData.get('shippingFee')) || 0;\n            data.previousBalance = parseFloat(formData.get('previousBalance')) || 0;\n            data.paidAmount = parseFloat(formData.get('paidAmount')) || 0;\n            data.notes = formData.get('notes');\n\n            // 商品資料\n            data.items = [];\n            const itemNames = formData.getAll('itemName[]');\n            const quantities = formData.getAll('quantity[]');\n            const unitPrices = formData.getAll('unitPrice[]');\n\n            for (let i = 0; i < itemNames.length; i++) {\n              if (itemNames[i].trim()) {\n                data.items.push({\n                  name: itemNames[i],\n                  quantity: parseFloat(quantities[i]),\n                  unitPrice: parseFloat(unitPrices[i]),\n                  amount: parseFloat(quantities[i]) * parseFloat(unitPrices[i])\n                });\n              }\n            }\n\n            // 計算總計和餘額\n            data.subtotal = data.items.reduce((sum, item) => sum + item.amount, 0);\n            data.total = data.subtotal + data.shippingFee;\n            data.balance = data.paidAmount;\n\n            // 將資料編碼並傳送到預覽頁面\n            const encodedData = encodeURIComponent(JSON.stringify(data));\n            const previewUrl = \\`/preview-invoice?data=\\${encodedData}\\`;\n\n            // 在新視窗開啟預覽\n            window.open(previewUrl, '_blank', 'width=800,height=1000,scrollbars=yes');\n          }\n\n          // 初始計算\n          calculateTotal();\n        </script>\n      </body>\n    </html>\n  `;\n\n  return new Response(html, {\n    status: 200,\n    headers: {\n      'Content-Type': 'text/html; charset=utf-8'\n    }\n  });\n}\n\n// 處理提交出貨記錄\nasync function handleSubmitInvoice(request, env) {\n  if (request.method !== 'POST') {\n    return new Response('Method not allowed', { status: 405 });\n  }\n\n  try {\n    const data = await request.json();\n\n    // TODO: 實現寫入 Google Sheets 的功能\n    // 1. 讀取現有的 sheet 資料\n    // 2. 計算新的餘額\n    // 3. 寫入新的記錄\n\n    // 暫時返回成功回應\n    return new Response(JSON.stringify({\n      success: true,\n      message: '出貨記錄已儲存',\n      data: data\n    }), {\n      status: 200,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: error.message\n    }), {\n      status: 500,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n}\n\n// 處理預覽出貨單\nasync function handleGetLastBalance(request, env) {\n  try {\n    const requestBody = await request.json();\n    const { fileId, customerId } = requestBody;\n\n    if (!fileId) {\n      return new Response(JSON.stringify({ error: '未提供檔案ID' }), {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' }\n      });\n    }\n\n    // 目前先返回模擬資料，因為需要完整的OAuth流程才能讀取私有Google Sheets\n    // 實際應用中應該通過OAuth獲取access token後使用Google Sheets API\n    let lastBalance = 0;\n\n    // 模擬從對應的Google Sheets檔案讀取最後一筆餘額\n    // 實際應用中會使用Google Sheets API來讀取fileId對應檔案的最後一筆餘額資料\n\n    // 為了演示，根據檔案ID提供不同的模擬餘額\n    const mockBalancesByFileId = {\n      '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ': 53455, // 百合藥局檔案\n      '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms': 24800, // 心芯藥局檔案\n      '1ZFGJKLmn9oPQRstuVWXyz1a2b3c4d5e6f7g8h9i0j': 15600, // 悅橙藥局檔案\n      '1ABCDEFGHijklMNOPqrsTUvwXYZ1234567890abcde': 82300, // 幸運草藥局檔案\n      '1QWERTYUIOPasdfGHJKLzxcvBNM1234567890qwert': 36700, // 繁華藥局檔案\n      '1ASDFGHjklQWERTYzxcvBNMuioPASDF1234567890': 19200, // 家欣藥局檔案\n      '1ZXCVBNMqwerTYUIoPASDfghJKLzxcVBN1234567890': 64500, // 西湖藥局檔案\n      '1POIUYTREWqasdFGHJklZXCvbnm1234567890POIUY': 41800, // 耀元藥局檔案\n      '1MNBVCXzasdQWErtYUIOpasFGHjkl1234567890MNB': 28900, // 愛維康藥局檔案\n      '1LKJHGFdsaZXCvbnMQWERtyuioP1234567890LKJHG': 56100, // 景賀藥局檔案\n      '1QAZwsxEDCrfvTGByhnUJMikOLp1234567890QAZws': 33400, // 辰鴻藥局檔案\n      '1PLMoknIJBuhVYgcTFrdXEswZaq1234567890PLMok': 48700, // 豐原福倫藥局檔案\n      '1OKMijnHubGYvtfCRdxEsw2za1234567890OKMij': 21600, // 北斗福倫藥局檔案\n      '1NJMuhbGYvtfCRdxesw2ZAq13579024681NJMuh': 37300, // 株一藥局檔案\n      '1MKLoinHubGYvtfCRdxesw2ZA1357902468MKLoi': 59800, // 嘉鶴藥局檔案\n      '1LJKhubGYvtfCRdxesw2ZAq135790246810LJKh': 26500  // 雪仁藥局檔案\n    };\n\n    lastBalance = mockBalancesByFileId[fileId] || 0;\n\n    return new Response(JSON.stringify({\n      lastBalance,\n      note: '這是模擬資料。實際部署時需要完整OAuth認證來讀取Google Sheets。'\n    }), {\n      headers: { 'Content-Type': 'application/json' }\n    });\n\n  } catch (error) {\n    return new Response(JSON.stringify({ error: '處理請求時發生錯誤: ' + error.message }), {\n      status: 500,\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n}\n\nasync function handlePreviewInvoice(request, env) {\n  try {\n    const url = new URL(request.url);\n    const encodedData = url.searchParams.get('data');\n\n    if (!encodedData) {\n      return new Response('缺少出貨單資料', { status: 400 });\n    }\n\n    const data = JSON.parse(decodeURIComponent(encodedData));\n\n    // 生成出貨單 HTML\n    const html = generateInvoiceHTML(data);\n\n    return new Response(html, {\n      status: 200,\n      headers: {\n        'Content-Type': 'text/html; charset=utf-8'\n      }\n    });\n\n  } catch (error) {\n    return new Response('解析出貨單資料時發生錯誤: ' + error.message, { status: 500 });\n  }\n}\n\n// 生成出貨單 HTML\nfunction generateInvoiceHTML(data) {\n  const invoiceNumber = 'INV' + new Date(data.invoiceDate).getFullYear() +\n                        String(new Date(data.invoiceDate).getMonth() + 1).padStart(2, '0') +\n                        String(new Date(data.invoiceDate).getDate()).padStart(2, '0') +\n                        String(Math.floor(Math.random() * 1000)).padStart(3, '0');\n\n  // 根據公司 ID 設定供應商資訊\n  let supplierInfo = {\n    name: '您的公司名稱',\n    phone: '06-2906741 / 0980347570',\n    address: '台南市東區崇學路165號5樓'\n  };\n\n  if (data.companyId === 'liang-xin') {\n    supplierInfo = {\n      name: '量心醫藥股份有限公司',\n      phone: '06-2906741 / 0980347570',\n      address: '台南市東區崇學路165號5樓'\n    };\n  } else if (data.companyId === 'jia-xuan') {\n    supplierInfo = {\n      name: '嘉萱漢方有限公司',\n      phone: '06-2906741 / 0980347570',\n      address: '台南市東區崇學路165號5樓'\n    };\n  }\n\n  return `\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>出貨單 - ${data.clientName}</title>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'Microsoft YaHei', '微軟正黑體', Arial, sans-serif;\n            background: #f5f7fa;\n            padding: 20px;\n            color: #333;\n          }\n\n          .invoice-container {\n            max-width: 800px;\n            margin: 0 auto;\n            background: white;\n            box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n            border-radius: 8px;\n            overflow: hidden;\n          }\n\n          .invoice-header {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            padding: 30px;\n            text-align: center;\n            position: relative;\n          }\n\n          .invoice-title {\n            font-size: 32px;\n            font-weight: bold;\n            margin-bottom: 10px;\n            letter-spacing: 2px;\n          }\n\n          .invoice-number {\n            font-size: 14px;\n            opacity: 0.9;\n            position: absolute;\n            top: 15px;\n            right: 30px;\n          }\n\n          .invoice-body {\n            padding: 40px;\n          }\n\n          .invoice-info {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 40px;\n            margin-bottom: 40px;\n            padding-bottom: 30px;\n            border-bottom: 2px solid #f1f3f4;\n          }\n\n          .info-section h3 {\n            color: #667eea;\n            font-size: 16px;\n            margin-bottom: 15px;\n            border-bottom: 1px solid #e9ecef;\n            padding-bottom: 5px;\n          }\n\n          .info-item {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 8px;\n            padding: 5px 0;\n          }\n\n          .info-label {\n            color: #666;\n            font-weight: 500;\n          }\n\n          .info-value {\n            font-weight: 600;\n            color: #333;\n          }\n\n          .items-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 30px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.05);\n            border-radius: 8px;\n            overflow: hidden;\n          }\n\n          .items-table th {\n            background: #f8f9fa;\n            color: #495057;\n            font-weight: 600;\n            padding: 15px 20px;\n            text-align: left;\n            border-bottom: 2px solid #dee2e6;\n          }\n\n          .items-table td {\n            padding: 15px 20px;\n            border-bottom: 1px solid #e9ecef;\n            vertical-align: middle;\n          }\n\n          .items-table tbody tr:hover {\n            background-color: #f8f9fa;\n          }\n\n          .items-table tbody tr:last-child td {\n            border-bottom: none;\n          }\n\n          .text-right {\n            text-align: right;\n          }\n\n          .text-center {\n            text-align: center;\n          }\n\n          .amount {\n            font-weight: 600;\n            font-family: 'Courier New', monospace;\n          }\n\n          .summary {\n            background: #f8f9fa;\n            border-radius: 8px;\n            padding: 25px;\n            margin-bottom: 30px;\n            border-left: 4px solid #667eea;\n          }\n\n          .summary-row {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            margin-bottom: 12px;\n            font-size: 16px;\n          }\n\n          .summary-row.total {\n            font-size: 20px;\n            font-weight: bold;\n            color: #667eea;\n            border-top: 2px solid #dee2e6;\n            padding-top: 15px;\n            margin-top: 20px;\n            margin-bottom: 0;\n          }\n\n          .notes {\n            background: #fff3cd;\n            border: 1px solid #ffeaa7;\n            border-radius: 8px;\n            padding: 20px;\n            margin-bottom: 30px;\n          }\n\n          .notes h4 {\n            color: #856404;\n            margin-bottom: 10px;\n          }\n\n          .notes p {\n            color: #856404;\n            line-height: 1.5;\n            margin: 0;\n          }\n\n          .footer {\n            text-align: center;\n            color: #6c757d;\n            font-size: 14px;\n            padding: 20px;\n            border-top: 1px solid #e9ecef;\n            background: #f8f9fa;\n          }\n\n          .actions {\n            text-align: center;\n            padding: 20px;\n            background: #f8f9fa;\n            border-top: 1px solid #e9ecef;\n          }\n\n          .btn {\n            display: inline-block;\n            padding: 12px 24px;\n            margin: 0 10px;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 16px;\n            font-weight: 500;\n            text-decoration: none;\n            transition: all 0.3s ease;\n          }\n\n          .btn-primary {\n            background: #667eea;\n            color: white;\n          }\n\n          .btn-primary:hover {\n            background: #5a67d8;\n            transform: translateY(-2px);\n            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n          }\n\n          .btn-secondary {\n            background: #6c757d;\n            color: white;\n          }\n\n          .btn-secondary:hover {\n            background: #5a6268;\n            transform: translateY(-2px);\n          }\n\n          @media print {\n            body {\n              background: white;\n              padding: 0;\n            }\n\n            .invoice-container {\n              box-shadow: none;\n              border-radius: 0;\n            }\n\n            .actions, .btn {\n              display: none !important;\n            }\n\n            .invoice-header {\n              background: #667eea !important;\n              -webkit-print-color-adjust: exact;\n              color-adjust: exact;\n            }\n\n            .summary {\n              background: #f8f9fa !important;\n              -webkit-print-color-adjust: exact;\n              color-adjust: exact;\n            }\n          }\n\n          @media (max-width: 768px) {\n            .invoice-info {\n              grid-template-columns: 1fr;\n              gap: 20px;\n            }\n\n            .items-table th,\n            .items-table td {\n              padding: 10px 15px;\n            }\n\n            .invoice-title {\n              font-size: 24px;\n            }\n\n            .btn {\n              display: block;\n              margin: 10px auto;\n              width: 200px;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"invoice-container\">\n          <div class=\"invoice-header\">\n            <div class=\"invoice-number\">單號: ${invoiceNumber}</div>\n            <div class=\"invoice-title\">出 貨 單</div>\n            <div>DELIVERY NOTE</div>\n          </div>\n\n          <div class=\"invoice-body\">\n            <div class=\"invoice-info\">\n              <div class=\"info-section\">\n                <h3>客戶資訊</h3>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">客戶名稱：</span>\n                  <span class=\"info-value\">${data.clientName}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">出貨日期：</span>\n                  <span class=\"info-value\">${new Date(data.invoiceDate).toLocaleDateString('zh-TW')}</span>\n                </div>\n              </div>\n\n              <div class=\"info-section\">\n                <h3>供應商資訊</h3>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">公司名稱：</span>\n                  <span class=\"info-value\">${supplierInfo.name}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">聯絡電話：</span>\n                  <span class=\"info-value\">${supplierInfo.phone}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">地址：</span>\n                  <span class=\"info-value\">${supplierInfo.address}</span>\n                </div>\n              </div>\n            </div>\n\n            <table class=\"items-table\">\n              <thead>\n                <tr>\n                  <th style=\"width: 50px;\">#</th>\n                  <th>商品名稱</th>\n                  <th class=\"text-center\" style=\"width: 100px;\">數量</th>\n                  <th class=\"text-right\" style=\"width: 120px;\">單價</th>\n                  <th class=\"text-right\" style=\"width: 120px;\">金額</th>\n                </tr>\n              </thead>\n              <tbody>\n                ${data.items.map((item, index) => `\n                  <tr>\n                    <td class=\"text-center\">${index + 1}</td>\n                    <td>${item.name}</td>\n                    <td class=\"text-center\">${item.quantity}</td>\n                    <td class=\"text-right amount\">$${item.unitPrice.toLocaleString('zh-TW', {minimumFractionDigits: 2})}</td>\n                    <td class=\"text-right amount\">$${item.amount.toLocaleString('zh-TW', {minimumFractionDigits: 2})}</td>\n                  </tr>\n                `).join('')}\n              </tbody>\n            </table>\n\n            <div class=\"summary\">\n              <div class=\"summary-row\">\n                <span>商品小計：</span>\n                <span class=\"amount\">$${data.subtotal.toLocaleString('zh-TW', {minimumFractionDigits: 2})}</span>\n              </div>\n              <div class=\"summary-row\">\n                <span>運費：</span>\n                <span class=\"amount\">$${data.shippingFee.toLocaleString('zh-TW', {minimumFractionDigits: 2})}</span>\n              </div>\n              <div class=\"summary-row total\">\n                <span>本次應付總計：</span>\n                <span class=\"amount\">$${data.total.toLocaleString('zh-TW', {minimumFractionDigits: 2})}</span>\n              </div>\n            </div>\n\n            <div class=\"summary\" style=\"background: #f0f8ff; border-left-color: #007bff;\">\n              <h4 style=\"color: #007bff; margin-bottom: 15px;\">帳務計算</h4>\n              <div class=\"summary-row\">\n                <span>前期餘額：</span>\n                <span class=\"amount\">$${(data.previousBalance || 0).toLocaleString('zh-TW', {minimumFractionDigits: 2})}</span>\n              </div>\n              <div class=\"summary-row\">\n                <span>加：本次應付：</span>\n                <span class=\"amount\">$${data.total.toLocaleString('zh-TW', {minimumFractionDigits: 2})}</span>\n              </div>\n              <div class=\"summary-row\">\n                <span>減：本次收款：</span>\n                <span class=\"amount\" style=\"color: #28a745;\">$${(data.paidAmount || 0).toLocaleString('zh-TW', {minimumFractionDigits: 2})}</span>\n              </div>\n              <div class=\"summary-row total\" style=\"background: ${data.balance === 0 ? '#d4edda' : data.balance < 0 ? '#f8d7da' : '#fff3cd'}; padding: 10px; border-radius: 5px;\">\n                <span style=\"color: ${data.balance === 0 ? '#155724' : data.balance < 0 ? '#721c24' : '#856404'};\">\n                  ${data.balance === 0 ? '✓ 已結清' : data.balance < 0 ? '客戶欠款：' : '客戶餘額：'}\n                </span>\n                <span class=\"amount\" style=\"color: ${data.balance === 0 ? '#155724' : data.balance < 0 ? '#721c24' : '#856404'}; font-size: 22px;\">\n                  $${data.balance < 0 ? data.balance.toLocaleString('zh-TW', {minimumFractionDigits: 2}) : (data.balance || 0).toLocaleString('zh-TW', {minimumFractionDigits: 2})}\n                </span>\n              </div>\n            </div>\n\n            ${data.notes ? `\n              <div class=\"notes\">\n                <h4>📝 備註</h4>\n                <p>${data.notes}</p>\n              </div>\n            ` : ''}\n          </div>\n\n          <div class=\"actions\">\n            <button class=\"btn btn-primary\" onclick=\"window.print()\">🖨️ 列印出貨單</button>\n            <button class=\"btn btn-secondary\" onclick=\"window.close()\">✕ 關閉視窗</button>\n          </div>\n\n          <div class=\"footer\">\n            <p>此出貨單由系統自動生成 | 生成時間：${new Date().toLocaleString('zh-TW')}</p>\n          </div>\n        </div>\n      </body>\n    </html>\n  `;\n}\n\n// 生成包含嵌入式 Google Sheets 的 HTML 頁面\nfunction getEmbeddedSheetPage(fileId, clientName) {\n  // Google Sheets 嵌入式編輯器 URL - 使用編輯模式參數\n  // rm=minimal 移除大部分 UI，widget=false 隱藏小工具，headers=false 隱藏標題\n  const embedUrl = `https://docs.google.com/spreadsheets/d/${fileId}/edit?rm=minimal&headers=false&widget=false`;\n\n  return `\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>${clientName ? clientName + ' - ' : ''}Google Sheets 編輯器</title>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            background: #f5f5f5;\n            display: flex;\n            flex-direction: column;\n            height: 100vh;\n            overflow: hidden;\n          }\n\n          .header {\n            background: #1a73e8;\n            color: white;\n            padding: 15px 20px;\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n          }\n\n          .header h1 {\n            font-size: 20px;\n            font-weight: 500;\n            display: flex;\n            align-items: center;\n            gap: 10px;\n          }\n\n          .client-name {\n            background: rgba(255,255,255,0.2);\n            padding: 5px 12px;\n            border-radius: 20px;\n            font-size: 14px;\n          }\n\n          .actions {\n            display: flex;\n            gap: 10px;\n          }\n\n          .btn {\n            background: white;\n            color: #1a73e8;\n            border: none;\n            padding: 8px 16px;\n            border-radius: 4px;\n            cursor: pointer;\n            font-size: 14px;\n            font-weight: 500;\n            text-decoration: none;\n            display: inline-flex;\n            align-items: center;\n            gap: 5px;\n            transition: all 0.3s ease;\n          }\n\n          .btn:hover {\n            box-shadow: 0 2px 8px rgba(0,0,0,0.15);\n            transform: translateY(-1px);\n          }\n\n          .iframe-container {\n            flex: 1;\n            position: relative;\n            background: white;\n            margin: 20px;\n            border-radius: 8px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n            overflow: hidden;\n          }\n\n          iframe {\n            width: 100%;\n            height: 100%;\n            border: none;\n          }\n\n          .loading {\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            text-align: center;\n            color: #666;\n          }\n\n          .spinner {\n            border: 3px solid #f3f3f3;\n            border-top: 3px solid #1a73e8;\n            border-radius: 50%;\n            width: 40px;\n            height: 40px;\n            animation: spin 1s linear infinite;\n            margin: 0 auto 20px;\n          }\n\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n\n          @media (max-width: 768px) {\n            .header {\n              flex-direction: column;\n              gap: 10px;\n              text-align: center;\n            }\n\n            .iframe-container {\n              margin: 10px;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"header\">\n          <h1>\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n              <polyline points=\"14 2 14 8 20 8\"></polyline>\n              <line x1=\"8\" y1=\"13\" x2=\"16\" y2=\"13\"></line>\n              <line x1=\"8\" y1=\"17\" x2=\"16\" y2=\"17\"></line>\n            </svg>\n            Google Sheets 編輯器\n            ${clientName ? `<span class=\"client-name\">${clientName}</span>` : ''}\n          </h1>\n          <div class=\"actions\">\n            <a href=\"https://docs.google.com/spreadsheets/d/${fileId}/edit\" target=\"_blank\" class=\"btn\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n                <polyline points=\"15 3 21 3 21 9\"></polyline>\n                <line x1=\"10\" y1=\"14\" x2=\"21\" y2=\"3\"></line>\n              </svg>\n              在新視窗開啟\n            </a>\n            <button onclick=\"location.reload()\" class=\"btn\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <polyline points=\"23 4 23 10 17 10\"></polyline>\n                <polyline points=\"1 20 1 14 7 14\"></polyline>\n                <path d=\"M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15\"></path>\n              </svg>\n              重新整理\n            </button>\n          </div>\n        </div>\n\n        <div class=\"iframe-container\">\n          <div class=\"loading\" id=\"loading\">\n            <div class=\"spinner\"></div>\n            <p>正在載入 Google Sheets...</p>\n          </div>\n          <iframe\n            id=\"sheet-iframe\"\n            src=\"${embedUrl}\"\n            onload=\"document.getElementById('loading').style.display='none'\"\n            onerror=\"handleError()\"\n            allowfullscreen>\n          </iframe>\n        </div>\n\n        <script>\n          function handleError() {\n            document.getElementById('loading').innerHTML =\n              '<p style=\"color: #d93025;\">無法載入 Google Sheets</p>' +\n              '<p style=\"margin-top: 10px; font-size: 14px;\">請確認您有存取權限</p>' +\n              '<button onclick=\"location.reload()\" class=\"btn\" style=\"margin-top: 20px;\">重試</button>';\n          }\n\n          // 超時處理\n          setTimeout(function() {\n            const loading = document.getElementById('loading');\n            if (loading && loading.style.display !== 'none') {\n              loading.innerHTML =\n                '<p style=\"color: #ff9800;\">載入時間較長...</p>' +\n                '<p style=\"margin-top: 10px; font-size: 14px;\">如果無法載入，請檢查您的網路連線</p>';\n            }\n          }, 10000);\n        </script>\n      </body>\n    </html>\n  `;\n}", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTH<PERSON>_EXPORTS from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/src/index.js\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"/opt/homebrew/lib/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"/opt/homebrew/lib/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/src/index.js\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/.wrangler/tmp/bundle-CMHBbr/middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"/opt/homebrew/lib/node_modules/wrangler/templates/middleware/common.ts\";\nimport type { WorkerEntrypointConstructor } from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/.wrangler/tmp/bundle-CMHBbr/middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/.wrangler/tmp/bundle-CMHBbr/middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA;AAAA,EACE,WAAa;AAAA,IACX;AAAA,MACE,IAAM;AAAA,MACN,MAAQ;AAAA,MACR,SAAW;AAAA,QACT;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,IAAM;AAAA,MACN,MAAQ;AAAA,MACR,SAAW;AAAA,QACT;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACrEA,IAAO,cAAQ;AAAA,EACb,MAAM,MAAM,SAAS,KAAK,KAAK;AAC7B,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,OAAO,IAAI;AAGjB,QAAI,SAAS,OAAO,SAAS,IAAI;AAC/B,aAAO,IAAI,SAAS,YAAY,GAAG;AAAA,QACjC,SAAS,EAAE,gBAAgB,YAAY;AAAA,MACzC,CAAC;AAAA,IACH;AAGA,QAAI,SAAS,mBAAmB;AAC9B,aAAO,oBAAoB,SAAS,GAAG;AAAA,IACzC;AAGA,QAAI,SAAS,SAAS;AACpB,aAAO,WAAW,GAAG;AAAA,IACvB;AAGA,QAAI,SAAS,eAAe;AAC1B,aAAO,gBAAgB,SAAS,GAAG;AAAA,IACrC;AAGA,QAAI,SAAS,YAAY;AACvB,aAAO,mBAAmB,SAAS,GAAG;AAAA,IACxC;AAGA,QAAI,SAAS,kBAAkB;AAC7B,aAAO,mBAAmB,SAAS,GAAG;AAAA,IACxC;AAGA,QAAI,SAAS,mBAAmB;AAC9B,aAAO,oBAAoB,SAAS,GAAG;AAAA,IACzC;AAGA,QAAI,SAAS,mBAAmB;AAC9B,aAAO,oBAAoB,SAAS,GAAG;AAAA,IACzC;AAGA,QAAI,SAAS,oBAAoB;AAC/B,aAAO,qBAAqB,SAAS,GAAG;AAAA,IAC1C;AAGA,QAAI,SAAS,yBAAyB;AACpC,aAAO,qBAAqB,SAAS,GAAG;AAAA,IAC1C;AAGA,WAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC;AAAA,EAClD;AACF;AAGA,SAAS,cAAc;AACrB,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0CT;AA3CS;AA8CT,eAAe,WAAW,KAAK;AAG7B,SAAO,IAAI,SAAS,oDAAY,EAAE,QAAQ,IAAI,CAAC;AACjD;AAJe;AAOf,eAAe,oBAAoB,SAAS,KAAK;AAG/C,SAAO,IAAI,SAAS,yDAAiB,EAAE,QAAQ,IAAI,CAAC;AACtD;AAJe;AAOf,eAAe,gBAAgB,SAAS,KAAK;AAG3C,SAAO,IAAI,SAAS,gEAAc,EAAE,QAAQ,IAAI,CAAC;AACnD;AAJe;AAOf,eAAe,mBAAmB,SAAS,KAAK;AAC9C,MAAI;AACF,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,SAAS,IAAI,aAAa,IAAI,QAAQ;AAC5C,UAAM,aAAa,IAAI,aAAa,IAAI,YAAY;AAEpD,QAAI,CAAC,QAAQ;AACX,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,mCAAe,CAAC,GAAG;AAAA,QAC7D,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAGA,UAAM,OAAO,qBAAqB,QAAQ,UAAU;AAEpD,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EAEH,SAAS,OAAO;AACd,YAAQ,MAAM,sEAAoB,KAAK;AACvC,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,OAAO;AAAA,MACP,SAAS,MAAM;AAAA,IACjB,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AACF;AAjCe;AAoCf,eAAe,mBAAmB,SAAS,KAAK;AAE9C,QAAM,gBAAgB,IAAI,mBAAmB;AAG7C,QAAM,eAAe,CAAC;AACtB,oBAAc,UAAU,QAAQ,aAAW;AACzC,YAAQ,QAAQ,QAAQ,YAAU;AAChC,mBAAa,KAAK;AAAA,QAChB,SAAS,QAAQ;AAAA,QACjB,MAAM,OAAO;AAAA,QACb,IAAI,OAAO;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAqQQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YASxB,kBAAc,UAAU,IAAI,aAAW;AAAA;AAAA,0CAET,QAAQ,IAAI;AAAA;AAAA,kBAEpC,QAAQ,QAAQ,IAAI,YAAU;AAAA;AAAA,iDAEC,OAAO,IAAI;AAAA;AAAA,iDAEX,aAAa,eAAe,mBAAmB,OAAO,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,wDAKpD,aAAa,eAAe,mBAAmB,OAAO,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAOlG,EAAE,KAAK,EAAE,CAAC;AAAA;AAAA;AAAA,WAGhB,EAAE,KAAK,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4EnB,SAAO,IAAI,SAAS,MAAM;AAAA,IACxB,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AAvYe;AA0Yf,eAAe,oBAAoB,SAAS,KAAK;AAC/C,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,QAAM,aAAa,IAAI,aAAa,IAAI,YAAY,KAAK;AACzD,QAAM,SAAS,IAAI,aAAa,IAAI,QAAQ,KAAK,IAAI,mBAAmB;AAGxE,MAAI,YAAY;AAChB,MAAI,cAAc;AAClB,MAAI,aAAa;AAEjB,aAAW,WAAW,kBAAc,WAAW;AAC7C,UAAM,SAAS,QAAQ,QAAQ,KAAK,OAAK,EAAE,SAAS,UAAU;AAC9D,QAAI,QAAQ;AACV,kBAAY,QAAQ;AACpB,oBAAc,QAAQ;AACtB,mBAAa,OAAO;AACpB;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA,wDAIW,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mCA2JhB,UAAU,KAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,0DAKY,MAAM;AAAA,8DACF,UAAU;AAAA,6DACX,SAAS;AAAA,+DACP,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCA+IxC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuN1C,SAAO,IAAI,SAAS,MAAM;AAAA,IACxB,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AAviBe;AA0iBf,eAAe,oBAAoB,SAAS,KAAK;AAC/C,MAAI,QAAQ,WAAW,QAAQ;AAC7B,WAAO,IAAI,SAAS,sBAAsB,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC3D;AAEA,MAAI;AACF,UAAM,OAAO,MAAM,QAAQ,KAAK;AAQhC,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,SAAS;AAAA,MACT;AAAA,IACF,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EAEH,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACf,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AApCe;AAuCf,eAAe,qBAAqB,SAAS,KAAK;AAChD,MAAI;AACF,UAAM,cAAc,MAAM,QAAQ,KAAK;AACvC,UAAM,EAAE,QAAQ,WAAW,IAAI;AAE/B,QAAI,CAAC,QAAQ;AACX,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,mCAAU,CAAC,GAAG;AAAA,QACxD,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAIA,QAAI,cAAc;AAMlB,UAAM,uBAAuB;AAAA,MAC3B,gDAAgD;AAAA;AAAA,MAChD,gDAAgD;AAAA;AAAA,MAChD,8CAA8C;AAAA;AAAA,MAC9C,8CAA8C;AAAA;AAAA,MAC9C,8CAA8C;AAAA;AAAA,MAC9C,6CAA6C;AAAA;AAAA,MAC7C,+CAA+C;AAAA;AAAA,MAC/C,8CAA8C;AAAA;AAAA,MAC9C,8CAA8C;AAAA;AAAA,MAC9C,8CAA8C;AAAA;AAAA,MAC9C,8CAA8C;AAAA;AAAA,MAC9C,8CAA8C;AAAA;AAAA,MAC9C,4CAA4C;AAAA;AAAA,MAC5C,2CAA2C;AAAA;AAAA,MAC3C,4CAA4C;AAAA;AAAA,MAC5C,2CAA2C;AAAA;AAAA,IAC7C;AAEA,kBAAc,qBAAqB,MAAM,KAAK;AAE9C,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC;AAAA,MACA,MAAM;AAAA,IACR,CAAC,GAAG;AAAA,MACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EAEH,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,6DAAgB,MAAM,QAAQ,CAAC,GAAG;AAAA,MAC5E,QAAQ;AAAA,MACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AACF;AAtDe;AAwDf,eAAe,qBAAqB,SAAS,KAAK;AAChD,MAAI;AACF,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,cAAc,IAAI,aAAa,IAAI,MAAM;AAE/C,QAAI,CAAC,aAAa;AAChB,aAAO,IAAI,SAAS,8CAAW,EAAE,QAAQ,IAAI,CAAC;AAAA,IAChD;AAEA,UAAM,OAAO,KAAK,MAAM,mBAAmB,WAAW,CAAC;AAGvD,UAAM,OAAO,oBAAoB,IAAI;AAErC,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EAEH,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,+EAAmB,MAAM,SAAS,EAAE,QAAQ,IAAI,CAAC;AAAA,EACvE;AACF;AAxBe;AA2Bf,SAAS,oBAAoB,MAAM;AACjC,QAAM,gBAAgB,QAAQ,IAAI,KAAK,KAAK,WAAW,EAAE,YAAY,IAC/C,OAAO,IAAI,KAAK,KAAK,WAAW,EAAE,SAAS,IAAI,CAAC,EAAE,SAAS,GAAG,GAAG,IACjE,OAAO,IAAI,KAAK,KAAK,WAAW,EAAE,QAAQ,CAAC,EAAE,SAAS,GAAG,GAAG,IAC5D,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,GAAI,CAAC,EAAE,SAAS,GAAG,GAAG;AAG9E,MAAI,eAAe;AAAA,IACjB,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAEA,MAAI,KAAK,cAAc,aAAa;AAClC,mBAAe;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,EACF,WAAW,KAAK,cAAc,YAAY;AACxC,mBAAe;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,EACF;AAEA,SAAO;AAAA;AAAA;AAAA;AAAA,sCAIc,KAAK,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wDAyRQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6CAWd,KAAK,UAAU;AAAA;AAAA;AAAA;AAAA,6CAIf,IAAI,KAAK,KAAK,WAAW,EAAE,mBAAmB,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6CAQtD,aAAa,IAAI;AAAA;AAAA;AAAA;AAAA,6CAIjB,aAAa,KAAK;AAAA;AAAA;AAAA;AAAA,6CAIlB,aAAa,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAgB/C,KAAK,MAAM,IAAI,CAAC,MAAM,UAAU;AAAA;AAAA,8CAEJ,QAAQ,CAAC;AAAA,0BAC7B,KAAK,IAAI;AAAA,8CACW,KAAK,QAAQ;AAAA,qDACN,KAAK,UAAU,eAAe,SAAS,EAAC,uBAAuB,EAAC,CAAC,CAAC;AAAA,qDAClE,KAAK,OAAO,eAAe,SAAS,EAAC,uBAAuB,EAAC,CAAC,CAAC;AAAA;AAAA,iBAEnG,EAAE,KAAK,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wCAOa,KAAK,SAAS,eAAe,SAAS,EAAC,uBAAuB,EAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,wCAIjE,KAAK,YAAY,eAAe,SAAS,EAAC,uBAAuB,EAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,wCAIpE,KAAK,MAAM,eAAe,SAAS,EAAC,uBAAuB,EAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yCAQ7D,KAAK,mBAAmB,GAAG,eAAe,SAAS,EAAC,uBAAuB,EAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,wCAI/E,KAAK,MAAM,eAAe,SAAS,EAAC,uBAAuB,EAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,iEAIrC,KAAK,cAAc,GAAG,eAAe,SAAS,EAAC,uBAAuB,EAAC,CAAC,CAAC;AAAA;AAAA,kEAExE,KAAK,YAAY,IAAI,YAAY,KAAK,UAAU,IAAI,YAAY,SAAS;AAAA,sCACrG,KAAK,YAAY,IAAI,YAAY,KAAK,UAAU,IAAI,YAAY,SAAS;AAAA,oBAC3F,KAAK,YAAY,IAAI,8BAAU,KAAK,UAAU,IAAI,mCAAU,gCAAO;AAAA;AAAA,qDAElC,KAAK,YAAY,IAAI,YAAY,KAAK,UAAU,IAAI,YAAY,SAAS;AAAA,qBACzG,KAAK,UAAU,IAAI,KAAK,QAAQ,eAAe,SAAS,EAAC,uBAAuB,EAAC,CAAC,KAAK,KAAK,WAAW,GAAG,eAAe,SAAS,EAAC,uBAAuB,EAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,cAKpK,KAAK,QAAQ;AAAA;AAAA;AAAA,qBAGN,KAAK,KAAK;AAAA;AAAA,gBAEf,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qHASkB,oBAAI,KAAK,GAAE,eAAe,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAMtE;AA9aS;AAibT,SAAS,qBAAqB,QAAQ,YAAY;AAGhD,QAAM,WAAW,0CAA0C,MAAM;AAEjE,SAAO;AAAA;AAAA;AAAA;AAAA,iBAIQ,aAAa,aAAa,QAAQ,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAqIvC,aAAa,6BAA6B,UAAU,YAAY,EAAE;AAAA;AAAA;AAAA,8DAGlB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBA0BjD,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4B3B;AAvMS;;;AC7oDT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": []}