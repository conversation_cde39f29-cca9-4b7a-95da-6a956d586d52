				import worker, * as OTH<PERSON>_EXPORTS from "/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/src/index.js";
				import * as __MIDDLEWARE_0__ from "/opt/homebrew/lib/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts";
import * as __MIDDLEWARE_1__ from "/opt/homebrew/lib/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts";

				export * from "/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/src/index.js";
				const MIDDLEWARE_TEST_INJECT = "__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__";
				export const __INTERNAL_WRANGLER_MIDDLEWARE__ = [
					
					__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default
				]
				export default worker;