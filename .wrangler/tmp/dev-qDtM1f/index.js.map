{"version": 3, "sources": ["../bundle-vFfCd4/strip-cf-connecting-ip-header.js", "../../../src/data/customers.json", "../../../src/index.js", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-vFfCd4/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-vFfCd4/middleware-loader.entry.ts"], "sourceRoot": "/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/.wrangler/tmp/dev-qDtM1f", "sourcesContent": ["function stripCfConnectingIPHeader(input, init) {\n\tconst request = new Request(input, init);\n\trequest.headers.delete(\"CF-Connecting-IP\");\n\treturn request;\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\treturn Reflect.apply(target, thisArg, [\n\t\t\tstripCfConnectingIPHeader.apply(null, argArray),\n\t\t]);\n\t},\n});\n", "{\n  \"companies\": [\n    {\n      \"id\": \"liang-xin\",\n      \"name\": \"量心\",\n      \"clients\": [\n        {\n          \"id\": \"lx-client1\",\n          \"name\": \"百合藥局\",\n          \"fileId\": \"10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ\"\n        },\n        {\n          \"id\": \"lx-client2\",\n          \"name\": \"心芯藥局\",\n          \"fileId\": \"1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0\"\n        }\n      ]\n    },\n    {\n      \"id\": \"jia-xuan\",\n      \"name\": \"嘉萱\",\n      \"clients\": [\n        {\n          \"id\": \"jx-client1\",\n          \"name\": \"悅橙藥局\",\n          \"fileId\": \"1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo\"\n        },\n        {\n          \"id\": \"jx-client2\",\n          \"name\": \"幸運草藥局\",\n          \"fileId\": \"1S-Qpj3U8DyY318wWC0xN0M7b31grDp0Kql6gMfDEnpo\"\n        },\n        {\n          \"id\": \"jx-client3\",\n          \"name\": \"繁華藥局\",\n          \"fileId\": \"1O--Clvs-JjINkGISkU6e4MXFIBxEyZG1Cq5ol8W8SaM\"\n        },\n        {\n          \"id\": \"jx-client4\",\n          \"name\": \"家欣藥局\",\n          \"fileId\": \"1RxkKQUL7cDJFFR91ApNrNiAYKh6lQT2CeXA7HdD1ZcU\"\n        },\n        {\n          \"id\": \"jx-client5\",\n          \"name\": \"西湖藥局\",\n          \"fileId\": \"1F-yTFPnllUkIWuHURgHXV-E31fMuUC83kC6q5AXfaSc\"\n        },\n        {\n          \"id\": \"jx-client6\",\n          \"name\": \"耀元藥局\",\n          \"fileId\": \"1Q16-R1YuBhHO0pGe-_HHQM7VkBLYiyOT5BA6TeXDuCA\"\n        },\n        {\n          \"id\": \"jx-client7\",\n          \"name\": \"愛維康藥局\",\n          \"fileId\": \"1rAiGha2Rp5-mNVXLOBrNQZW3MlE-8PKuMxx0A7wiKtE\"\n        },\n        {\n          \"id\": \"jx-client8\",\n          \"name\": \"景賀藥局\",\n          \"fileId\": \"1g6Lj32CubziW3PuxKridRBJCEW0V6vppMfz60EhDHlI\"\n        },\n        {\n          \"id\": \"jx-client9\",\n          \"name\": \"辰鴻藥局\",\n          \"fileId\": \"1ZIbx7MtUiempMfp4NDxyoyLY3HE1LkSSfrFgQouju2I\"\n        },\n        {\n          \"id\": \"jx-client10\",\n          \"name\": \"豐原福倫藥局\",\n          \"fileId\": \"1TwTsnGO5MXKhX9pK7nL9xQAVCndfGt4tksznFiLrt50\"\n        },\n        {\n          \"id\": \"jx-client11\",\n          \"name\": \"北斗福倫藥局\",\n          \"fileId\": \"166vUmqixAedk_ds9DzopISBI2pRfKBzVQu0eL4xTlxQ\"\n        },\n        {\n          \"id\": \"jx-client12\",\n          \"name\": \"株一藥局\",\n          \"fileId\": \"1s1Qem6dAOGp06SVxGI6_Q57YBS2WwMNBO0HjuxEzoy0\"\n        },\n        {\n          \"id\": \"jx-client13\",\n          \"name\": \"嘉鶴藥局\",\n          \"fileId\": \"1MPUu5CjHIPraccilf5AvKhLBLnJdmPEOINlsgABTdI4\"\n        },\n        {\n          \"id\": \"jx-client14\",\n          \"name\": \"雪仁藥局\",\n          \"fileId\": \"1Hatilpfnrcm2rn-8HsTOxiY-U56waMflbCzzHRuc1ak\"\n        }\n      ]\n    }\n  ]\n}\n", "/**\n * Google Drive Excel 提取器\n *\n * 這個Cloudflare Worker允許用戶連接到Google Drive\n * 並提取Excel文件中的數據\n */\n\nimport customersData from './data/customers.json';\n\n// 處理HTTP請求的主要入口點\nexport default {\n  async fetch(request, env, ctx) {\n    const url = new URL(request.url);\n    const path = url.pathname;\n\n    // 主頁 - 顯示登入按鈕\n    if (path === \"/\" || path === \"\") {\n      return new Response(getHomePage(), {\n        headers: { \"Content-Type\": \"text/html\" },\n      });\n    }\n\n    // 處理OAuth回調\n    if (path === \"/oauth/callback\") {\n      return handleOAuthCallback(request, env);\n    }\n\n    // 處理認證，生成Google Drive授權URL\n    if (path === \"/auth\") {\n      return handleAuth(env);\n    }\n\n    // 列出Google Drive中的Excel文件\n    if (path === \"/list-files\") {\n      return handleListFiles(request, env);\n    }\n\n    // 提取Excel文件數據\n    if (path === \"/extract\") {\n      return handleExtractExcel(request, env);\n    }\n\n    // 客戶表單頁面\n    if (path === \"/customer-form\") {\n      return handleCustomerForm(request, env);\n    }\n\n    // 創建出貨記錄頁面\n    if (path === \"/create-invoice\") {\n      return handleCreateInvoice(request, env);\n    }\n\n    // 提交出貨記錄\n    if (path === \"/submit-invoice\") {\n      return handleSubmitInvoice(request, env);\n    }\n\n    // 預覽出貨單\n    if (path === \"/preview-invoice\") {\n      return handlePreviewInvoice(request, env);\n    }\n\n    // API: 獲取最後一筆餘額\n    if (path === \"/api/get-last-balance\") {\n      return handleGetLastBalance(request, env);\n    }\n\n    // API: 獲取商品清單（從Excel檔案）\n    if (path === \"/api/get-products\") {\n      return handleGetProductsFromExcel(request, env);\n    }\n\n    // 所有其他路徑返回404\n    return new Response(\"Not found\", { status: 404 });\n  },\n};\n\n// 返回簡單的HTML主頁，包含登入按鈕\nfunction getHomePage() {\n  return `\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>Google Drive Excel 提取器</title>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <style>\n          body {\n            font-family: Arial, sans-serif;\n            max-width: 800px;\n            margin: 0 auto;\n            padding: 20px;\n            line-height: 1.6;\n          }\n          button {\n            background-color: #4285f4;\n            color: white;\n            border: none;\n            padding: 10px 20px;\n            border-radius: 4px;\n            cursor: pointer;\n            font-size: 16px;\n          }\n          button:hover {\n            background-color: #3367d6;\n          }\n          .container {\n            margin-top: 50px;\n            text-align: center;\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <h1>📋 出貨單系統</h1>\n          <p>這個系統可以幫助您管理藥局出貨單，並從 Google Sheets 中讀取真實的餘額資料</p>\n          <div style=\"margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;\">\n            <strong>授權後可以：</strong><br>\n            • 從真實的 Google Sheets 讀取前期餘額<br>\n            • 自動更新客戶餘額資料<br>\n            • 完整的出貨單管理功能\n          </div>\n          <button onclick=\"window.location.href='/auth'\" style=\"margin: 10px;\">🔐 授權 Google Drive 存取</button>\n          <button onclick=\"window.location.href='/customer-form'\" style=\"margin: 10px; background-color: #6c757d;\">🚀 直接使用（模擬資料）</button>\n        </div>\n      </body>\n    </html>\n  `;\n}\n\n// 處理認證並重定向到Google的OAuth頁面\nasync function handleAuth(env) {\n  const clientId = env.CLIENT_ID;\n  const redirectUri = env.REDIRECT_URI;\n\n  // Google OAuth 2.0 scope for Google Sheets - 使用更廣泛的權限\n  const scopes = [\n    'https://www.googleapis.com/auth/spreadsheets',\n    'https://www.googleapis.com/auth/drive',\n    'https://www.googleapis.com/auth/drive.readonly'\n  ].join(' ');\n\n  const authUrl = new URL('https://accounts.google.com/o/oauth2/auth');\n  authUrl.searchParams.set('client_id', clientId);\n  authUrl.searchParams.set('redirect_uri', redirectUri);\n  authUrl.searchParams.set('response_type', 'code');\n  authUrl.searchParams.set('scope', scopes);\n  authUrl.searchParams.set('access_type', 'offline');\n  authUrl.searchParams.set('prompt', 'consent');\n\n  return Response.redirect(authUrl.toString(), 302);\n}\n\n// 處理從Google重定向回來的OAuth回調\nasync function handleOAuthCallback(request, env) {\n  const url = new URL(request.url);\n  const code = url.searchParams.get('code');\n  const error = url.searchParams.get('error');\n\n  if (error) {\n    return new Response(`OAuth認證錯誤: ${error}`, { status: 400 });\n  }\n\n  if (!code) {\n    return new Response('未收到授權碼', { status: 400 });\n  }\n\n  try {\n    // 交換授權碼為存取令牌\n    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n      body: new URLSearchParams({\n        client_id: env.CLIENT_ID,\n        client_secret: env.CLIENT_SECRET,\n        code: code,\n        grant_type: 'authorization_code',\n        redirect_uri: env.REDIRECT_URI,\n      }),\n    });\n\n    if (!tokenResponse.ok) {\n      const errorText = await tokenResponse.text();\n      return new Response(`令牌交換失敗: ${errorText}`, { status: 400 });\n    }\n\n    const tokens = await tokenResponse.json();\n\n    // 將令牌存儲在cookie中和localStorage中\n    const response = new Response(`\n      <!DOCTYPE html>\n      <html>\n        <head>\n          <title>授權成功</title>\n          <meta charset=\"UTF-8\">\n          <style>\n            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }\n            .success { color: #28a745; }\n            .token-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }\n          </style>\n        </head>\n        <body>\n          <h1 class=\"success\">✅ 授權成功！</h1>\n          <p>您已成功連接到 Google Drive。現在可以讀取真實的 Google Sheets 資料了。</p>\n          <div class=\"token-info\">\n            <small>Token已保存，有效期：${tokens.expires_in || 3600}秒</small>\n          </div>\n          <button onclick=\"window.location.href='/customer-form'\" style=\"background: #007bff; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;\">\n            前往出貨單系統\n          </button>\n\n          <script>\n            // 同時存儲在localStorage中作為備用\n            localStorage.setItem('access_token', '${tokens.access_token}');\n            localStorage.setItem('token_expires', Date.now() + ${(tokens.expires_in || 3600) * 1000});\n            if ('${tokens.refresh_token}') {\n              localStorage.setItem('refresh_token', '${tokens.refresh_token}');\n            }\n          </script>\n        </body>\n      </html>\n    `, {\n      headers: {\n        'Content-Type': 'text/html',\n        'Set-Cookie': [\n          `access_token=${tokens.access_token}; Path=/; Max-Age=${tokens.expires_in || 3600}; SameSite=Lax`,\n          `token_type=${tokens.token_type || 'Bearer'}; Path=/; Max-Age=${tokens.expires_in || 3600}; SameSite=Lax`\n        ]\n      }\n    });\n\n    return response;\n  } catch (error) {\n    return new Response(`OAuth處理錯誤: ${error.message}`, { status: 500 });\n  }\n}\n\n// 列出用戶Google Drive中的Excel文件\nasync function handleListFiles(request, env) {\n  // 這裡將列出Google Drive中的文件\n  // 目前是一個簡單的佔位符\n  return new Response(\"列出文件功能尚未實現\", { status: 501 });\n}\n\n// 從指定的Excel文件中提取數據並顯示嵌入式編輯器\nasync function handleExtractExcel(request, env) {\n  try {\n    const url = new URL(request.url);\n    const fileId = url.searchParams.get('fileId');\n    const clientName = url.searchParams.get('clientName');\n\n    if (!fileId) {\n      return new Response(JSON.stringify({ error: '缺少 fileId 參數' }), {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' }\n      });\n    }\n\n    // 返回包含嵌入式 Google Sheets 的 HTML 頁面\n    const html = getEmbeddedSheetPage(fileId, clientName);\n\n    return new Response(html, {\n      status: 200,\n      headers: {\n        'Content-Type': 'text/html; charset=utf-8'\n      }\n    });\n\n  } catch (error) {\n    console.error('顯示Excel編輯器時發生錯誤:', error);\n    return new Response(JSON.stringify({\n      error: '顯示Excel編輯器時發生錯誤',\n      details: error.message\n    }), {\n      status: 500,\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n}\n\n// 根據客戶ID獲取對應的檔案ID（從JSON資料讀取）\nfunction getFileIdForCustomerId(customerId) {\n  for (const company of customersData.companies) {\n    const client = company.clients.find(c => c.id === customerId);\n    if (client && client.fileId) {\n      return client.fileId;\n    }\n  }\n  return '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ'; // 預設檔案ID\n}\n\n// 處理客戶表單頁面\nasync function handleCustomerForm(request, env) {\n\n  // 從 JSON 檔案讀取所有客戶\n  const allCustomers = [];\n  customersData.companies.forEach(company => {\n    company.clients.forEach(client => {\n      allCustomers.push({\n        company: company.name,\n        name: client.name,\n        id: client.id,\n        fileId: client.fileId || '請設定檔案ID'\n      });\n    });\n  });\n\n  const html = `\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>選擇客戶 - 出貨單系統</title>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            min-height: 100vh;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            padding: 20px;\n          }\n\n          .container {\n            background: white;\n            border-radius: 20px;\n            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n            padding: 40px;\n            max-width: 500px;\n            width: 100%;\n          }\n\n          h1 {\n            color: #333;\n            text-align: center;\n            margin-bottom: 10px;\n            font-size: 28px;\n          }\n\n          .subtitle {\n            text-align: center;\n            color: #666;\n            margin-bottom: 30px;\n            font-size: 14px;\n          }\n\n          .form-group {\n            margin-bottom: 25px;\n          }\n\n          label {\n            display: block;\n            color: #555;\n            font-weight: 500;\n            margin-bottom: 10px;\n            font-size: 14px;\n          }\n\n          select, input {\n            width: 100%;\n            padding: 12px 15px;\n            border: 2px solid #e0e0e0;\n            border-radius: 10px;\n            font-size: 16px;\n            transition: all 0.3s ease;\n            background: white;\n          }\n\n          select:focus, input:focus {\n            outline: none;\n            border-color: #667eea;\n            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n          }\n\n          .company-section {\n            margin-bottom: 25px;\n          }\n\n          .company-title {\n            color: #555;\n            font-size: 16px;\n            font-weight: 600;\n            margin-bottom: 10px;\n            padding-left: 5px;\n            border-left: 3px solid #667eea;\n          }\n\n          .customer-list {\n            display: grid;\n            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n            gap: 15px;\n            margin-top: 15px;\n          }\n\n          .customer-card {\n            background: white;\n            border: 2px solid #e1e5e9;\n            border-radius: 12px;\n            padding: 20px;\n            transition: all 0.3s ease;\n          }\n\n          .customer-card:hover {\n            border-color: #667eea;\n            transform: translateY(-2px);\n            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);\n          }\n\n          .customer-name {\n            font-size: 18px;\n            font-weight: 600;\n            color: #333;\n            margin-bottom: 15px;\n            text-align: center;\n          }\n\n          .customer-actions {\n            display: flex;\n            gap: 8px;\n          }\n\n          .btn-action {\n            flex: 1;\n            padding: 10px 15px;\n            border: none;\n            border-radius: 8px;\n            cursor: pointer;\n            font-size: 14px;\n            font-weight: 500;\n            text-decoration: none;\n            text-align: center;\n            transition: all 0.3s ease;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: 5px;\n          }\n\n          .btn-view {\n            background: #f8f9fa;\n            color: #495057;\n            border: 1px solid #dee2e6;\n          }\n\n          .btn-view:hover {\n            background: #e9ecef;\n            transform: translateY(-1px);\n          }\n\n          .btn-create {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n          }\n\n          .btn-create:hover {\n            transform: translateY(-1px);\n            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n          }\n\n          .submit-button {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            border: none;\n            padding: 15px 30px;\n            border-radius: 10px;\n            cursor: pointer;\n            font-size: 18px;\n            font-weight: 600;\n            width: 100%;\n            transition: all 0.3s ease;\n            margin-top: 20px;\n          }\n\n          .submit-button:hover:not(:disabled) {\n            transform: translateY(-2px);\n            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n          }\n\n          .submit-button:disabled {\n            opacity: 0.5;\n            cursor: not-allowed;\n          }\n\n          .divider {\n            text-align: center;\n            color: #999;\n            margin: 30px 0;\n            position: relative;\n          }\n\n          .divider::before {\n            content: '';\n            position: absolute;\n            top: 50%;\n            left: 0;\n            right: 0;\n            height: 1px;\n            background: #e0e0e0;\n          }\n\n          .divider span {\n            background: white;\n            padding: 0 15px;\n            position: relative;\n          }\n\n          .custom-input-group {\n            display: flex;\n            gap: 10px;\n            align-items: flex-end;\n          }\n\n          .custom-input-group input {\n            flex: 1;\n          }\n\n          .custom-input-group button {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            border: none;\n            padding: 12px 20px;\n            border-radius: 10px;\n            cursor: pointer;\n            font-size: 16px;\n            font-weight: 500;\n            transition: all 0.3s ease;\n            white-space: nowrap;\n          }\n\n          .custom-input-group button:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);\n          }\n\n          @media (max-width: 480px) {\n            .container {\n              padding: 30px 20px;\n            }\n\n            h1 {\n              font-size: 24px;\n            }\n\n            .customer-list {\n              grid-template-columns: 1fr;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <h1>📋 出貨單系統</h1>\n          <p class=\"subtitle\">選擇客戶以開啟對應的出貨單</p>\n\n          <div class=\"form-group\">\n            <label for=\"fileId\">Google Sheets 檔案 ID</label>\n            <input\n              type=\"text\"\n              id=\"fileId\"\n              name=\"fileId\"\n              value=\"每個客戶有獨立的檔案ID\"\n              readonly\n              placeholder=\"輸入 Google Sheets 檔案 ID\"\n            />\n          </div>\n\n          <div class=\"divider\">\n            <span>快速選擇客戶</span>\n          </div>\n\n          ${customersData.companies.map(company => `\n            <div class=\"company-section\">\n              <h3 class=\"company-title\">${company.name}</h3>\n              <div class=\"customer-list\">\n                ${company.clients.map(client => {\n    return `\n                  <div class=\"customer-card\">\n                    <div class=\"customer-name\">${client.name}</div>\n                    <div class=\"customer-id\" style=\"font-size: 12px; color: #666; margin: 5px 0;\">\n                      檔案ID: ${client.fileId || '請設定檔案ID'}\n                    </div>\n                    <div class=\"customer-actions\">\n                      <a href=\"/extract?fileId=${client.fileId}&clientName=${encodeURIComponent(client.name)}\"\n                         class=\"btn-action btn-view\">\n                        📊 查看記錄\n                      </a>\n                      <a href=\"/create-invoice?fileId=${client.fileId}&clientName=${encodeURIComponent(client.name)}\"\n                         class=\"btn-action btn-create\">\n                        📝 建立出貨單\n                      </a>\n                    </div>\n                  </div>\n                  `;\n  }).join('')}\n              </div>\n            </div>\n          `).join('')}\n\n          <div class=\"divider\">\n            <span>或輸入自訂客戶名稱</span>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"customClient\">自訂客戶名稱</label>\n            <div class=\"custom-input-group\">\n              <input\n                type=\"text\"\n                id=\"customClient\"\n                name=\"customClient\"\n                placeholder=\"輸入客戶名稱\"\n              />\n              <button onclick=\"goToCustomClient()\">開啟</button>\n            </div>\n          </div>\n        </div>\n\n        <script>\n          // 每個客戶現在都有固定的檔案ID，不需要動態更新連結\n\n          // 前往自訂客戶（使用預設檔案ID）\n          function goToCustomClient() {\n            const clientName = document.getElementById('customClient').value;\n\n            if (!clientName.trim()) {\n              alert('請輸入客戶名稱');\n              return;\n            }\n\n            // 使用預設檔案ID作為自訂客戶\n            const defaultFileId = '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ';\n            window.location.href = \\`/extract?fileId=\\${encodeURIComponent(defaultFileId)}&clientName=\\${encodeURIComponent(clientName)}\\`;\n          }\n\n          // Enter 鍵送出\n          document.getElementById('customClient').addEventListener('keypress', function(e) {\n            if (e.key === 'Enter') {\n              goToCustomClient();\n            }\n          });\n        </script>\n      </body>\n    </html>\n  `;\n\n  return new Response(html, {\n    status: 200,\n    headers: {\n      'Content-Type': 'text/html; charset=utf-8'\n    }\n  });\n}\n\n// 處理創建出貨記錄頁面\nasync function handleCreateInvoice(request, env) {\n  const url = new URL(request.url);\n  const clientName = url.searchParams.get('clientName') || '';\n  const fileId = url.searchParams.get('fileId') || env.DEFAULT_FILE_ID || '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ';\n\n  // 根據客戶名稱找出所屬公司和客戶ID\n  let companyId = '';\n  let companyName = '';\n  let customerId = '';\n\n  for (const company of customersData.companies) {\n    const client = company.clients.find(c => c.name === clientName);\n    if (client) {\n      companyId = company.id;\n      companyName = company.name;\n      customerId = client.id;\n      break;\n    }\n  }\n\n  const html = `\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>創建出貨記錄 - ${clientName}</title>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            background: #f5f7fa;\n            min-height: 100vh;\n            padding: 20px;\n          }\n\n          .container {\n            max-width: 800px;\n            margin: 0 auto;\n            background: white;\n            border-radius: 12px;\n            box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n            overflow: hidden;\n          }\n\n          .header {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            padding: 20px;\n            text-align: center;\n          }\n\n          .form-section {\n            padding: 30px;\n          }\n\n          .form-group {\n            margin-bottom: 20px;\n          }\n\n          label {\n            display: block;\n            color: #555;\n            font-weight: 500;\n            margin-bottom: 8px;\n          }\n\n          input, select, textarea {\n            width: 100%;\n            padding: 12px;\n            border: 2px solid #e1e5e9;\n            border-radius: 8px;\n            font-size: 16px;\n            transition: border-color 0.3s;\n          }\n\n          input:focus, select:focus, textarea:focus {\n            outline: none;\n            border-color: #667eea;\n          }\n\n          .row {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 15px;\n          }\n\n          .items-section {\n            border-top: 1px solid #e1e5e9;\n            padding-top: 20px;\n            margin-top: 20px;\n          }\n\n          .item-row {\n            display: grid;\n            grid-template-columns: 2fr 1fr 1fr 1fr auto;\n            gap: 10px;\n            align-items: end;\n            margin-bottom: 10px;\n            padding: 10px;\n            background: #f8f9fa;\n            border-radius: 8px;\n          }\n\n          .btn {\n            padding: 12px 20px;\n            border: none;\n            border-radius: 8px;\n            cursor: pointer;\n            font-weight: 500;\n            transition: all 0.3s;\n          }\n\n          .btn-primary {\n            background: #667eea;\n            color: white;\n          }\n\n          .btn-success {\n            background: #28a745;\n            color: white;\n          }\n\n          .btn-danger {\n            background: #dc3545;\n            color: white;\n          }\n\n          .btn-secondary {\n            background: #6c757d;\n            color: white;\n          }\n\n          .btn:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n          }\n\n          .summary {\n            background: #f8f9fa;\n            padding: 20px;\n            border-radius: 8px;\n            margin-top: 20px;\n          }\n\n          .summary-row {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 10px;\n          }\n\n          .total {\n            font-size: 18px;\n            font-weight: bold;\n            color: #333;\n            border-top: 2px solid #667eea;\n            padding-top: 10px;\n          }\n\n          @media (max-width: 768px) {\n            .row {\n              grid-template-columns: 1fr;\n            }\n\n            .item-row {\n              grid-template-columns: 1fr;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <h1>📋 創建出貨記錄</h1>\n            <p>客戶：${clientName} (${companyName})</p>\n          </div>\n\n          <div class=\"form-section\">\n            <form id=\"invoiceForm\">\n              <input type=\"hidden\" name=\"fileId\" value=\"${fileId}\">\n              <input type=\"hidden\" name=\"clientName\" value=\"${clientName}\">\n              <input type=\"hidden\" name=\"companyId\" value=\"${companyId}\">\n              <input type=\"hidden\" name=\"companyName\" value=\"${companyName}\">\n\n              <div class=\"row\">\n                <div class=\"form-group\">\n                  <label for=\"invoiceDate\">出貨日期</label>\n                  <input type=\"date\" id=\"invoiceDate\" name=\"invoiceDate\" required>\n                </div>\n                <div class=\"form-group\">\n                  <label for=\"shippingFee\">郵費</label>\n                  <input type=\"number\" id=\"shippingFee\" name=\"shippingFee\" min=\"0\" value=\"0\" onchange=\"calculateTotal()\">\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"form-group\">\n                  <label for=\"previousBalance\">前期餘額</label>\n                  <div style=\"display: flex; gap: 10px; align-items: center;\">\n                    <input type=\"number\" id=\"previousBalance\" name=\"previousBalance\" step=\"0.01\" value=\"0\" onchange=\"calculateTotal()\" readonly style=\"flex: 1;\">\n                    <button type=\"button\" onclick=\"fetchLastBalance()\" style=\"padding: 8px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">從Excel抓取</button>\n                  </div>\n                </div>\n                <div class=\"form-group\">\n                  <label for=\"paidAmount\">本次餘額</label>\n                  <input type=\"number\" id=\"paidAmount\" name=\"paidAmount\" min=\"0\" step=\"0.01\" value=\"0\" onchange=\"calculateTotal()\">\n                </div>\n              </div>\n\n              <div class=\"items-section\">\n                <h3>商品明細</h3>\n                <div id=\"itemsList\">\n                  <div class=\"item-row\">\n                    <div>\n                      <label>商品名稱</label>\n                      <select name=\"itemName[]\" required onchange=\"onProductChange(this)\">\n                        <option value=\"\">請選擇商品</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label>數量</label>\n                      <input type=\"number\" name=\"quantity[]\" min=\"1\" value=\"1\" required onchange=\"calculateTotal()\">\n                    </div>\n                    <div>\n                      <label>單價</label>\n                      <input type=\"number\" name=\"unitPrice[]\" min=\"0\" step=\"0.01\" required onchange=\"calculateTotal()\" readonly>\n                    </div>\n                    <div>\n                      <label>金額</label>\n                      <input type=\"number\" name=\"amount[]\" readonly>\n                    </div>\n                    <div>\n                      <label>&nbsp;</label>\n                      <button type=\"button\" class=\"btn btn-danger\" onclick=\"removeItem(this)\">刪除</button>\n                    </div>\n                  </div>\n                </div>\n\n                <button type=\"button\" class=\"btn btn-secondary\" onclick=\"addItem()\">+ 新增商品</button>\n              </div>\n\n              <div class=\"form-group\">\n                <label for=\"notes\">備註</label>\n                <textarea id=\"notes\" name=\"notes\" rows=\"3\" placeholder=\"輸入備註資訊\"></textarea>\n              </div>\n\n              <div class=\"summary\">\n                <div class=\"summary-row\">\n                  <span>商品小計：</span>\n                  <span id=\"subtotal\">$0</span>\n                </div>\n                <div class=\"summary-row\">\n                  <span>郵費：</span>\n                  <span id=\"shippingDisplay\">$0</span>\n                </div>\n                <div class=\"summary-row total\">\n                  <span>本次應付總計：</span>\n                  <span id=\"total\">$0</span>\n                </div>\n                <div style=\"margin: 20px 0; border-top: 1px dashed #dee2e6;\"></div>\n                <div class=\"summary-row\">\n                  <span>前期餘額：</span>\n                  <span id=\"previousBalanceDisplay\">$0</span>\n                </div>\n                <div class=\"summary-row\">\n                  <span>減：本次應付：</span>\n                  <span id=\"currentTotalDisplay\">$0</span>\n                </div>\n                <div class=\"summary-row\">\n                  <span>本次餘額：</span>\n                  <span id=\"paidAmountDisplay\">$0</span>\n                </div>\n              </div>\n\n              <div style=\"margin-top: 30px; text-align: center;\">\n                <button type=\"submit\" class=\"btn btn-success\" style=\"margin-right: 10px;\">💾 儲存記錄</button>\n                <button type=\"button\" class=\"btn btn-primary\" onclick=\"previewInvoice()\">👁️ 預覽出貨單</button>\n              </div>\n            </form>\n          </div>\n        </div>\n\n        <script>\n          // 商品資料\n          let productsData = [];\n\n          // 設定今天的日期為預設值\n          document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];\n\n          // 載入商品資料\n          async function loadProducts() {\n            try {\n              const fileId = document.querySelector('input[name=\"fileId\"]').value;\n\n              // 準備headers，包括可能的access token\n              const headers = {\n                'Content-Type': 'application/json'\n              };\n\n              // 嘗試從localStorage獲取access token\n              const accessToken = localStorage.getItem('access_token');\n              if (accessToken) {\n                headers['Authorization'] = 'Bearer ' + accessToken;\n              }\n\n              const response = await fetch('/api/get-products', {\n                method: 'POST',\n                headers: headers,\n                body: JSON.stringify({\n                  fileId: fileId\n                })\n              });\n\n              const result = await response.json();\n              if (result.success) {\n                productsData = result.products;\n                populateProductOptions();\n\n                // 顯示讀取來源資訊\n                if (result.note) {\n                  console.log('商品清單來源:', result.note);\n                }\n              } else {\n                console.error('載入商品資料失敗:', result.error);\n                // 使用預設的模擬資料\n                productsData = [\n                  { id: 'demo1', name: '範例商品A', price: 100, unit: '盒' },\n                  { id: 'demo2', name: '範例商品B', price: 200, unit: '瓶' }\n                ];\n                populateProductOptions();\n              }\n            } catch (error) {\n              console.error('載入商品資料失敗:', error);\n              // 使用預設的模擬資料\n              productsData = [\n                { id: 'demo1', name: '範例商品A', price: 100, unit: '盒' },\n                { id: 'demo2', name: '範例商品B', price: 200, unit: '瓶' }\n              ];\n              populateProductOptions();\n            }\n          }\n\n          // 填充商品選項到所有下拉選單\n          function populateProductOptions() {\n            const selects = document.querySelectorAll('select[name=\"itemName[]\"]');\n            selects.forEach(select => {\n              // 保留目前選擇的值\n              const currentValue = select.value;\n\n              // 清空選項並重新填充\n              select.innerHTML = '<option value=\"\">請選擇商品</option>';\n\n              productsData.forEach(product => {\n                const option = document.createElement('option');\n                option.value = product.name;\n                option.textContent = \\`\\${product.name} - $\\${product.price} (\\${product.unit})\\`;\n                option.dataset.price = product.price;\n                option.dataset.productId = product.id;\n                select.appendChild(option);\n              });\n\n              // 恢復之前的選擇\n              if (currentValue) {\n                select.value = currentValue;\n              }\n            });\n          }\n\n          // 商品選擇變更時的處理\n          function onProductChange(selectElement) {\n            const selectedOption = selectElement.options[selectElement.selectedIndex];\n            const priceInput = selectElement.closest('.item-row').querySelector('input[name=\"unitPrice[]\"]');\n\n            if (selectedOption.dataset.price) {\n              priceInput.value = selectedOption.dataset.price;\n            } else {\n              priceInput.value = '';\n            }\n\n            calculateTotal();\n          }\n\n          function addItem() {\n            const itemsList = document.getElementById('itemsList');\n            const newItem = document.createElement('div');\n            newItem.className = 'item-row';\n            newItem.innerHTML = \\`\n              <div>\n                <label>商品名稱</label>\n                <select name=\"itemName[]\" required onchange=\"onProductChange(this)\">\n                  <option value=\"\">請選擇商品</option>\n                </select>\n              </div>\n              <div>\n                <label>數量</label>\n                <input type=\"number\" name=\"quantity[]\" min=\"1\" value=\"1\" required onchange=\"calculateTotal()\">\n              </div>\n              <div>\n                <label>單價</label>\n                <input type=\"number\" name=\"unitPrice[]\" min=\"0\" step=\"0.01\" required onchange=\"calculateTotal()\" readonly>\n              </div>\n              <div>\n                <label>金額</label>\n                <input type=\"number\" name=\"amount[]\" readonly>\n              </div>\n              <div>\n                <label>&nbsp;</label>\n                <button type=\"button\" class=\"btn btn-danger\" onclick=\"removeItem(this)\">刪除</button>\n              </div>\n            \\`;\n            itemsList.appendChild(newItem);\n\n            // 為新增的下拉選單填充商品選項\n            populateProductOptions();\n          }\n\n          function removeItem(button) {\n            if (document.querySelectorAll('.item-row').length > 1) {\n              button.closest('.item-row').remove();\n              calculateTotal();\n            }\n          }\n\n          async function fetchLastBalance() {\n            const customerId = '${customerId}';\n            const fileId = getFileIdForCustomer(customerId);\n\n            if (!fileId) {\n              alert('找不到對應的Excel檔案ID');\n              return;\n            }\n\n            try {\n              // 準備headers，包括可能的access token\n              const headers = {\n                'Content-Type': 'application/json'\n              };\n\n              // 嘗試從localStorage獲取access token\n              const accessToken = localStorage.getItem('access_token');\n              if (accessToken) {\n                headers['Authorization'] = 'Bearer ' + accessToken;\n              }\n\n              // 調用後端API來抓取最後一筆餘額\n              const response = await fetch('/api/get-last-balance', {\n                method: 'POST',\n                headers: headers,\n                body: JSON.stringify({\n                  fileId: fileId,\n                  customerId: customerId\n                })\n              });\n\n              if (response.ok) {\n                const result = await response.json();\n                document.getElementById('previousBalance').value = result.lastBalance || 0;\n\n                // 設定表單中的隱藏 fileId 欄位\n                const fileIdInput = document.querySelector('input[name=\"fileId\"]');\n                if (fileIdInput) {\n                  fileIdInput.value = fileId;\n                }\n\n                calculateTotal();\n              } else {\n                alert('抓取餘額失敗: ' + response.statusText);\n              }\n            } catch (error) {\n              alert('抓取餘額時發生錯誤: ' + error.message);\n            }\n          }\n\n          function getFileIdForCustomer(customerId) {\n            // 從客戶資料中讀取對應的檔案ID\n            const customersData = ${JSON.stringify(customersData)};\n            for (const company of customersData.companies) {\n              const client = company.clients.find(c => c.id === customerId);\n              if (client && client.fileId) {\n                return client.fileId;\n              }\n            }\n            return '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ'; // 預設檔案ID\n          }\n\n          function calculateTotal() {\n            let subtotal = 0;\n            const itemRows = document.querySelectorAll('.item-row');\n\n            itemRows.forEach(row => {\n              const quantity = parseFloat(row.querySelector('input[name=\"quantity[]\"]').value) || 0;\n              const unitPrice = parseFloat(row.querySelector('input[name=\"unitPrice[]\"]').value) || 0;\n              const amount = quantity * unitPrice;\n\n              row.querySelector('input[name=\"amount[]\"]').value = amount.toFixed(2);\n              subtotal += amount;\n            });\n\n            const shippingFee = parseFloat(document.getElementById('shippingFee').value) || 0;\n            const total = subtotal + shippingFee;\n            const previousBalance = parseFloat(document.getElementById('previousBalance').value) || 0;\n            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;\n\n            // 餘額計算：前期餘額 - 本次應付總計 = 本次餘額\n            const balance = previousBalance - total;\n\n            // 更新顯示\n            document.getElementById('subtotal').textContent = '$' + subtotal.toFixed(2);\n            document.getElementById('shippingDisplay').textContent = '$' + shippingFee.toFixed(2);\n            document.getElementById('total').textContent = '$' + total.toFixed(2);\n            document.getElementById('previousBalanceDisplay').textContent = '$' + previousBalance.toFixed(2);\n            document.getElementById('currentTotalDisplay').textContent = '$' + total.toFixed(2);\n            document.getElementById('paidAmountDisplay').textContent = '$' + balance.toFixed(2);\n\n            // 同步本次餘額到最上面的輸入欄位（暫時移除事件監聽器避免循環）\n            const paidAmountElement = document.getElementById('paidAmount');\n            paidAmountElement.onchange = null;\n            paidAmountElement.value = balance.toFixed(2);\n            paidAmountElement.onchange = calculateTotal;\n\n          }\n\n          document.getElementById('invoiceForm').addEventListener('submit', function(e) {\n            e.preventDefault();\n\n            // 收集表單資料\n            const formData = new FormData(this);\n            const data = {};\n\n            // 基本資料\n            data.fileId = formData.get('fileId');\n            data.clientName = formData.get('clientName');\n            data.companyId = formData.get('companyId');\n            data.companyName = formData.get('companyName');\n            data.invoiceDate = formData.get('invoiceDate');\n            data.shippingFee = parseFloat(formData.get('shippingFee')) || 0;\n            data.previousBalance = parseFloat(formData.get('previousBalance')) || 0;\n            data.paidAmount = parseFloat(formData.get('paidAmount')) || 0;\n            data.notes = formData.get('notes');\n\n            // 商品資料\n            data.items = [];\n            const itemNames = formData.getAll('itemName[]');\n            const quantities = formData.getAll('quantity[]');\n            const unitPrices = formData.getAll('unitPrice[]');\n\n            for (let i = 0; i < itemNames.length; i++) {\n              if (itemNames[i].trim()) {\n                data.items.push({\n                  name: itemNames[i],\n                  quantity: parseFloat(quantities[i]),\n                  unitPrice: parseFloat(unitPrices[i]),\n                  amount: parseFloat(quantities[i]) * parseFloat(unitPrices[i])\n                });\n              }\n            }\n\n            // 計算總計和餘額\n            data.subtotal = data.items.reduce((sum, item) => sum + item.amount, 0);\n            data.total = data.subtotal + data.shippingFee;\n            data.balance = (data.previousBalance || 0) - data.total;\n\n            // 提交資料\n            fetch('/submit-invoice', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify(data)\n            })\n            .then(response => response.json())\n            .then(result => {\n              if (result.success) {\n                alert('出貨記錄已成功儲存！');\n                // 可以重新導向或清空表單\n                window.location.href = '/customer-form';\n              } else {\n                alert('儲存失敗：' + result.error);\n              }\n            })\n            .catch(error => {\n              alert('提交時發生錯誤：' + error.message);\n            });\n          });\n\n          function previewInvoice() {\n            // 收集表單資料並開啟預覽\n            const form = document.getElementById('invoiceForm');\n            const formData = new FormData(form);\n            const data = {};\n\n            // 基本資料\n            data.fileId = formData.get('fileId');\n            data.clientName = formData.get('clientName');\n            data.companyId = formData.get('companyId');\n            data.companyName = formData.get('companyName');\n            data.invoiceDate = formData.get('invoiceDate');\n            data.shippingFee = parseFloat(formData.get('shippingFee')) || 0;\n            data.previousBalance = parseFloat(formData.get('previousBalance')) || 0;\n            data.paidAmount = parseFloat(formData.get('paidAmount')) || 0;\n            data.notes = formData.get('notes');\n\n            // 商品資料\n            data.items = [];\n            const itemNames = formData.getAll('itemName[]');\n            const quantities = formData.getAll('quantity[]');\n            const unitPrices = formData.getAll('unitPrice[]');\n\n            for (let i = 0; i < itemNames.length; i++) {\n              if (itemNames[i].trim()) {\n                data.items.push({\n                  name: itemNames[i],\n                  quantity: parseFloat(quantities[i]),\n                  unitPrice: parseFloat(unitPrices[i]),\n                  amount: parseFloat(quantities[i]) * parseFloat(unitPrices[i])\n                });\n              }\n            }\n\n            // 計算總計和餘額\n            data.subtotal = data.items.reduce((sum, item) => sum + item.amount, 0);\n            data.total = data.subtotal + data.shippingFee;\n            data.balance = (data.previousBalance || 0) - data.total;\n\n            // 將資料編碼並傳送到預覽頁面\n            const encodedData = encodeURIComponent(JSON.stringify(data));\n            const previewUrl = \\`/preview-invoice?data=\\${encodedData}\\`;\n\n            // 在新視窗開啟預覽\n            window.open(previewUrl, '_blank', 'width=800,height=1000,scrollbars=yes');\n          }\n\n          // 頁面載入時初始化\n          document.addEventListener('DOMContentLoaded', function() {\n            loadProducts();\n            calculateTotal();\n          });\n\n          // 如果DOMContentLoaded已經觸發，直接執行初始化\n          if (document.readyState === 'loading') {\n            // 文檔還在載入中，等待DOMContentLoaded事件\n          } else {\n            // 文檔已經載入完成，直接執行初始化\n            loadProducts();\n            calculateTotal();\n          }\n        </script>\n      </body>\n    </html>\n  `;\n\n  return new Response(html, {\n    status: 200,\n    headers: {\n      'Content-Type': 'text/html; charset=utf-8'\n    }\n  });\n}\n\n// 處理提交出貨記錄\nasync function handleSubmitInvoice(request, env) {\n  if (request.method !== 'POST') {\n    return new Response('Method not allowed', { status: 405 });\n  }\n\n  try {\n    const data = await request.json();\n\n    // 獲取檔案 ID\n    const fileId = data.fileId;\n    if (!fileId) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: '缺少檔案ID'\n      }), {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' }\n      });\n    }\n\n    // 獲取 access token，優先使用 API Key\n    const accessToken = getAccessToken(request);\n\n    try {\n      // 準備要寫入的資料行\n      const newRow = [\n        data.invoiceDate,  // A欄：日期\n        data.items.map(item => `${item.name} x${item.quantity}`).join(', '), // B欄：品名\n        data.items.reduce((sum, item) => sum + item.quantity, 0), // C欄：總數量\n        data.total,        // D欄：價錢\n        data.total,        // E欄：小計\n        data.balance       // F欄：剩餘金額\n      ];\n\n      let writeSuccess = false;\n\n      // 先嘗試用 API Key 寫入\n      if (env.GOOGLE_API_KEY && !writeSuccess) {\n        try {\n          const appendUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F:append?valueInputOption=USER_ENTERED&key=${env.GOOGLE_API_KEY}`;\n          const response = await fetch(appendUrl, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              range: 'A:F',\n              majorDimension: 'ROWS',\n              values: [newRow]\n            })\n          });\n\n          if (response.ok) {\n            writeSuccess = true;\n            console.log('使用 API Key 成功寫入');\n          } else {\n            console.error('API Key 寫入失敗:', await response.text());\n          }\n        } catch (error) {\n          console.error('API Key 寫入錯誤:', error);\n        }\n      }\n\n      // 如果 API Key 失敗，嘗試用 OAuth\n      if (accessToken && !writeSuccess) {\n        try {\n          const appendUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F:append?valueInputOption=USER_ENTERED`;\n          const response = await fetch(appendUrl, {\n            method: 'POST',\n            headers: {\n              'Authorization': `Bearer ${accessToken}`,\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              range: 'A:F',\n              majorDimension: 'ROWS',\n              values: [newRow]\n            })\n          });\n\n          if (response.ok) {\n            writeSuccess = true;\n            console.log('使用 OAuth 成功寫入');\n          } else {\n            console.error('OAuth 寫入失敗:', await response.text());\n          }\n        } catch (error) {\n          console.error('OAuth 寫入錯誤:', error);\n        }\n      }\n\n      if (writeSuccess) {\n        return new Response(JSON.stringify({\n          success: true,\n          message: '出貨記錄已成功儲存到 Google Sheets',\n          data: data\n        }), {\n          status: 200,\n          headers: { 'Content-Type': 'application/json' }\n        });\n      } else {\n        return new Response(JSON.stringify({\n          success: false,\n          error: '無法寫入 Google Sheets，請檢查權限設定或進行 OAuth 認證'\n        }), {\n          status: 500,\n          headers: { 'Content-Type': 'application/json' }\n        });\n      }\n\n    } catch (writeError) {\n      console.error('寫入 Google Sheets 失敗:', writeError);\n      return new Response(JSON.stringify({\n        success: false,\n        error: '寫入失敗: ' + writeError.message\n      }), {\n        status: 500,\n        headers: { 'Content-Type': 'application/json' }\n      });\n    }\n\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: error.message\n    }), {\n      status: 500,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n}\n\n// 處理預覽出貨單\n// 從cookie或header獲取access token\nfunction getAccessToken(request) {\n  // 先嘗試從Cookie獲取\n  const cookieHeader = request.headers.get('Cookie');\n  if (cookieHeader) {\n    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {\n      const [key, value] = cookie.trim().split('=');\n      acc[key] = value;\n      return acc;\n    }, {});\n    if (cookies.access_token) {\n      return cookies.access_token;\n    }\n  }\n\n  // 也可以從Authorization header獲取\n  const authHeader = request.headers.get('Authorization');\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.slice(7);\n  }\n\n  return null;\n}\n\nasync function handleGetLastBalance(request, env) {\n  try {\n    const requestBody = await request.json();\n    const { fileId, customerId } = requestBody;\n\n    if (!fileId) {\n      return new Response(JSON.stringify({ error: '未提供檔案ID' }), {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' }\n      });\n    }\n\n    // 獲取access token\n    const accessToken = getAccessToken(request);\n\n    // 優先嘗試用 API Key（更可靠）\n    if (env.GOOGLE_API_KEY) {\n      try {\n        const apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F?key=${env.GOOGLE_API_KEY}`;\n        console.log('沒有 OAuth token，直接使用 API Key:', apiUrl);\n\n        const response = await fetch(apiUrl, {\n          headers: {\n            'Accept': 'application/json'\n          }\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          let lastBalance = 0;\n          // 從最後一行開始尋找有餘額資料的行（餘額在F欄，索引為5）\n          if (data.values && data.values.length > 0) {\n            for (let i = data.values.length - 1; i >= 0; i--) {\n              const row = data.values[i];\n              if (row && row[5] && !isNaN(parseFloat(row[5]))) {\n                lastBalance = parseFloat(row[5]);\n                console.log('從API Key找到餘額（F欄）:', lastBalance);\n                break;\n              }\n            }\n          }\n          return new Response(JSON.stringify({\n            lastBalance,\n            note: '使用API Key從Google Sheets讀取的資料'\n          }), {\n            headers: { 'Content-Type': 'application/json' }\n          });\n        } else {\n          const errorText = await response.text();\n          console.error('API Key 請求失敗:', response.status, errorText);\n        }\n      } catch (error) {\n        console.error('API Key 存取失敗:', error);\n      }\n    }\n\n    if (!accessToken) {\n      // 如果沒有access token，返回模擬資料\n      const mockBalancesByFileId = {\n        '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ': 53455, // 百合藥局檔案\n        '1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0': 24800, // 心芯藥局檔案\n        '1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo': 15600, // 悅橙藥局檔案\n        '1S-Qpj3U8DyY318wWC0xN0M7b31grDp0Kql6gMfDEnpo': 82300, // 幸運草藥局檔案\n        '1O--Clvs-JjINkGISkU6e4MXFIBxEyZG1Cq5ol8W8SaM': 36700, // 繁華藥局檔案\n        '1RxkKQUL7cDJFFR91ApNrNiAYKh6lQT2CeXA7HdD1ZcU': 19200, // 家欣藥局檔案\n        '1F-yTFPnllUkIWuHURgHXV-E31fMuUC83kC6q5AXfaSc': 64500, // 西湖藥局檔案\n        '1Q16-R1YuBhHO0pGe-_HHQM7VkBLYiyOT5BA6TeXDuCA': 41800, // 耀元藥局檔案\n        '1rAiGha2Rp5-mNVXLOBrNQZW3MlE-8PKuMxx0A7wiKtE': 28900, // 愛維康藥局檔案\n        '1g6Lj32CubziW3PuxKridRBJCEW0V6vppMfz60EhDHlI': 56100, // 景賀藥局檔案\n        '1ZIbx7MtUiempMfp4NDxyoyLY3HE1LkSSfrFgQouju2I': 33400, // 辰鴻藥局檔案\n        '1TwTsnGO5MXKhX9pK7nL9xQAVCndfGt4tksznFiLrt50': 48700, // 豐原福倫藥局檔案\n        '166vUmqixAedk_ds9DzopISBI2pRfKBzVQu0eL4xTlxQ': 21600, // 北斗福倫藥局檔案\n        '1s1Qem6dAOGp06SVxGI6_Q57YBS2WwMNBO0HjuxEzoy0': 37300, // 株一藥局檔案\n        '1MPUu5CjHIPraccilf5AvKhLBLnJdmPEOINlsgABTdI4': 59800, // 嘉鶴藥局檔案\n        '1Hatilpfnrcm2rn-8HsTOxiY-U56waMflbCzzHRuc1ak': 26500  // 雪仁藥局檔案\n      };\n\n      const lastBalance = mockBalancesByFileId[fileId] || 0;\n      return new Response(JSON.stringify({\n        lastBalance,\n        note: '這是模擬資料。請先進行OAuth認證以讀取真實的Google Sheets資料。'\n      }), {\n        headers: { 'Content-Type': 'application/json' }\n      });\n    }\n\n    // 使用真實的Google Sheets API讀取資料（只有有 OAuth token 時才執行）\n    if (accessToken) {\n      try {\n        // 先嘗試獲取檔案的metadata來檢查權限和工作表資訊\n        const metadataUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}`;\n\n        console.log('正在檢查檔案權限:', metadataUrl);\n        console.log('使用Access Token:', accessToken.substring(0, 20) + '...');\n\n        const metadataResponse = await fetch(metadataUrl, {\n          headers: {\n            'Authorization': `Bearer ${accessToken}`,\n            'Accept': 'application/json'\n          }\n        });\n\n        if (!metadataResponse.ok) {\n          const errorText = await metadataResponse.text();\n          console.error('檔案metadata取得失敗:', errorText);\n\n          // 直接嘗試讀取資料，不指定工作表名稱\n          const fallbackUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:E`;\n          console.log('嘗試fallback API:', fallbackUrl);\n        } else {\n          const metadata = await metadataResponse.json();\n          console.log('檔案資訊:', {\n            title: metadata.properties?.title,\n            sheets: metadata.sheets?.map(s => s.properties?.title)\n          });\n        }\n\n        // 嘗試多種可能的工作表名稱 - 讀取到F欄以包含餘額\n        let apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F`;\n\n        // 如果有metadata，使用第一個工作表的實際名稱\n        if (metadataResponse.ok) {\n          const metadata = await metadataResponse.json();\n          if (metadata.sheets && metadata.sheets.length > 0) {\n            const firstSheetName = metadata.sheets[0].properties.title;\n            apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/'${firstSheetName}'!A:F`;\n            console.log('使用工作表名稱:', firstSheetName);\n          }\n        }\n\n        console.log('正在呼叫Google Sheets API:', apiUrl);\n\n        // 先嘗試使用OAuth token\n        let response = await fetch(apiUrl, {\n          headers: {\n            'Authorization': `Bearer ${accessToken}`,\n            'Accept': 'application/json'\n          }\n        });\n\n        // 如果OAuth失敗，嘗試使用API Key\n        if (!response.ok) {\n          console.log('OAuth呼叫失敗，嘗試API Key方式');\n          const apiKeyUrl = `${apiUrl}?key=${env.GOOGLE_API_KEY}`;\n          console.log('使用API Key URL:', apiKeyUrl);\n\n          response = await fetch(apiKeyUrl, {\n            headers: {\n              'Accept': 'application/json'\n            }\n          });\n\n          // 如果API Key也失敗，嘗試公開CSV匯出（最後手段）\n          if (!response.ok) {\n            console.log('API Key也失敗，嘗試CSV匯出方式');\n            const csvUrl = `https://docs.google.com/spreadsheets/d/${fileId}/export?format=csv`;\n            console.log('嘗試CSV URL:', csvUrl);\n\n            const csvResponse = await fetch(csvUrl);\n            if (csvResponse.ok) {\n              const csvText = await csvResponse.text();\n              console.log('CSV資料長度:', csvText.length);\n\n              // 簡單解析CSV找最後一筆餘額 - 餘額在F欄（索引5）\n              const lines = csvText.split('\\n').filter(line => line.trim());\n              for (let i = lines.length - 1; i >= 0; i--) {\n                const cols = lines[i].split(',');\n                if (cols[5] && !isNaN(parseFloat(cols[5]))) {\n                  const balance = parseFloat(cols[5]);\n                  console.log('從CSV找到餘額（F欄）:', balance);\n                  return new Response(JSON.stringify({\n                    lastBalance: balance,\n                    note: '從CSV匯出讀取的資料（F欄餘額）'\n                  }), {\n                    headers: { 'Content-Type': 'application/json' }\n                  });\n                }\n              }\n            }\n\n            // 設定response為CSV響應以便後續統一處理\n            response = csvResponse;\n          }\n        }\n\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('Google Sheets API錯誤:', errorText);\n          console.error('Response status:', response.status, response.statusText);\n\n          // 根據錯誤類型提供不同的回應\n          let errorNote = '';\n          if (response.status === 401) {\n            errorNote = '認證失效或未授權。請重新進行OAuth認證，目前使用模擬資料。';\n          } else if (response.status === 403) {\n            errorNote = '無權限存取此Google Sheets檔案。請確認：1) 檔案已分享給授權帳戶，或 2) 檔案設為「知道連結的使用者可檢視」';\n          } else if (response.status === 404) {\n            errorNote = '找不到指定的Google Sheets檔案。請檢查檔案ID是否正確';\n          } else {\n            errorNote = `API呼叫失敗：${response.status} ${response.statusText}`;\n          }\n\n          // 如果API失敗，回退到模擬資料\n          const mockBalancesByFileId = {\n            '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ': 53455,\n            '1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0': 24800,\n            '1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo': 15600\n          };\n\n          const lastBalance = mockBalancesByFileId[fileId] || 0;\n          return new Response(JSON.stringify({\n            lastBalance,\n            note: errorNote,\n            fallback: true\n          }), {\n            headers: { 'Content-Type': 'application/json' }\n          });\n        }\n\n        const data = await response.json();\n        let lastBalance = 0;\n\n        // 從最後一行開始尋找有餘額資料的行（餘額在F欄，索引為5）\n        if (data.values && data.values.length > 0) {\n          for (let i = data.values.length - 1; i >= 0; i--) {\n            const row = data.values[i];\n            if (row && row[5] && !isNaN(parseFloat(row[5]))) {\n              lastBalance = parseFloat(row[5]);\n              console.log('從API找到餘額（F欄）:', lastBalance);\n              break;\n            }\n          }\n        }\n\n        return new Response(JSON.stringify({\n          lastBalance,\n          note: '從Google Sheets讀取的真實資料'\n        }), {\n          headers: { 'Content-Type': 'application/json' }\n        });\n\n      } catch (apiError) {\n        console.error('Google Sheets API呼叫錯誤:', apiError);\n\n        // 如果API呼叫失敗，回退到模擬資料\n        const mockBalancesByFileId = {\n          '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ': 53455,\n          '1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0': 24800,\n          '1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo': 15600\n        };\n\n        const lastBalance = mockBalancesByFileId[fileId] || 0;\n        return new Response(JSON.stringify({\n          lastBalance,\n          note: `API呼叫異常，使用模擬資料。錯誤: ${apiError.message}`\n        }), {\n          headers: { 'Content-Type': 'application/json' }\n        });\n      }\n    }\n\n  } catch (error) {\n    return new Response(JSON.stringify({ error: '處理請求時發生錯誤: ' + error.message }), {\n      status: 500,\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n}\n\n// 處理從Excel檔案獲取商品清單API\nasync function handleGetProductsFromExcel(request, env) {\n  try {\n    const requestBody = await request.json();\n    const { fileId } = requestBody;\n\n    if (!fileId) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: '未提供檔案ID'\n      }), {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' }\n      });\n    }\n\n    // 獲取access token\n    const accessToken = getAccessToken(request);\n    let products = [];\n\n    // 優先嘗試用 API Key 讀取商品清單\n    if (env.GOOGLE_API_KEY) {\n      try {\n        // 先嘗試讀取檔案的metadata來找到所有工作表\n        const metadataUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}?key=${env.GOOGLE_API_KEY}`;\n        const metadataResponse = await fetch(metadataUrl, {\n          headers: {\n            'Accept': 'application/json'\n          }\n        });\n\n        let productSheetName = '商品清單'; // 預設工作表名稱\n\n        if (metadataResponse.ok) {\n          const metadata = await metadataResponse.json();\n          console.log('檔案工作表:', metadata.sheets?.map(s => s.properties?.title));\n\n          // 尋找可能的商品清單工作表名稱\n          const possibleNames = ['商品清單', '商品', '產品清單', '產品', 'Products', 'Items', '工作品'];\n          const sheetNames = metadata.sheets?.map(s => s.properties?.title) || [];\n\n          for (const name of possibleNames) {\n            if (sheetNames.includes(name)) {\n              productSheetName = name;\n              break;\n            }\n          }\n\n          // 如果找不到，使用第二個工作表（假設第一個是出貨記錄）\n          if (!sheetNames.includes(productSheetName) && sheetNames.length > 1) {\n            productSheetName = sheetNames[1];\n          }\n        }\n\n        // 讀取商品清單工作表\n        const apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/'${productSheetName}'!A:C?key=${env.GOOGLE_API_KEY}`;\n        console.log('讀取商品清單:', apiUrl);\n\n        const response = await fetch(apiUrl, {\n          headers: {\n            'Accept': 'application/json'\n          }\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          console.log('商品清單原始資料:', data);\n\n          if (data.values && data.values.length > 0) {\n            // 解析商品資料，假設格式為：品名(A欄) | 價格(B欄) | 單位(C欄)\n            // 從第一行開始讀取，不跳過任何行\n            for (let i = 0; i < data.values.length; i++) {\n              const row = data.values[i];\n              if (row && row[0] && row[1]) { // 至少要有品名和價格\n                const product = {\n                  id: `prod_${i + 1}`,\n                  name: row[0].toString().trim(),\n                  price: parseFloat(row[1]) || 0,\n                  unit: row[2] ? row[2].toString().trim() : '個'\n                };\n\n                if (product.name && product.price > 0) {\n                  products.push(product);\n                }\n              }\n            }\n          }\n\n          console.log('解析後的商品清單:', products);\n\n          return new Response(JSON.stringify({\n            success: true,\n            products: products,\n            sheetName: productSheetName,\n            note: `從工作表「${productSheetName}」讀取的商品清單`\n          }), {\n            headers: { 'Content-Type': 'application/json' }\n          });\n        } else {\n          console.error('讀取商品清單失敗:', await response.text());\n        }\n      } catch (error) {\n        console.error('API Key 讀取商品清單失敗:', error);\n      }\n    }\n\n    // 如果API Key失敗，嘗試OAuth\n    if (accessToken && products.length === 0) {\n      try {\n        // 類似的邏輯，使用OAuth token\n        const metadataUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}`;\n        const metadataResponse = await fetch(metadataUrl, {\n          headers: {\n            'Authorization': `Bearer ${accessToken}`,\n            'Accept': 'application/json'\n          }\n        });\n\n        let productSheetName = '商品清單';\n\n        if (metadataResponse.ok) {\n          const metadata = await metadataResponse.json();\n          const possibleNames = ['商品清單', '商品', '產品清單', '產品', 'Products', 'Items', '工作品'];\n          const sheetNames = metadata.sheets?.map(s => s.properties?.title) || [];\n\n          for (const name of possibleNames) {\n            if (sheetNames.includes(name)) {\n              productSheetName = name;\n              break;\n            }\n          }\n\n          if (!sheetNames.includes(productSheetName) && sheetNames.length > 1) {\n            productSheetName = sheetNames[1];\n          }\n        }\n\n        const apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/'${productSheetName}'!A:C`;\n        const response = await fetch(apiUrl, {\n          headers: {\n            'Authorization': `Bearer ${accessToken}`,\n            'Accept': 'application/json'\n          }\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n\n          if (data.values && data.values.length > 0) {\n            // 從第一行開始讀取，不跳過任何行\n            for (let i = 0; i < data.values.length; i++) {\n              const row = data.values[i];\n              if (row && row[0] && row[1]) {\n                const product = {\n                  id: `prod_${i + 1}`,\n                  name: row[0].toString().trim(),\n                  price: parseFloat(row[1]) || 0,\n                  unit: row[2] ? row[2].toString().trim() : '個'\n                };\n\n                if (product.name && product.price > 0) {\n                  products.push(product);\n                }\n              }\n            }\n          }\n\n          return new Response(JSON.stringify({\n            success: true,\n            products: products,\n            sheetName: productSheetName,\n            note: `從工作表「${productSheetName}」讀取的商品清單`\n          }), {\n            headers: { 'Content-Type': 'application/json' }\n          });\n        }\n      } catch (error) {\n        console.error('OAuth 讀取商品清單失敗:', error);\n      }\n    }\n\n    // 如果都失敗，返回模擬資料\n    return new Response(JSON.stringify({\n      success: true,\n      products: [\n        { id: 'demo1', name: '範例商品A', price: 100, unit: '盒' },\n        { id: 'demo2', name: '範例商品B', price: 200, unit: '瓶' },\n        { id: 'demo3', name: '範例商品C', price: 150, unit: '包' }\n      ],\n      note: '無法讀取Excel檔案，使用模擬資料。請確認檔案權限或進行OAuth認證。'\n    }), {\n      headers: { 'Content-Type': 'application/json' }\n    });\n\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: '獲取商品清單時發生錯誤: ' + error.message\n    }), {\n      status: 500,\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n}\n\nasync function handlePreviewInvoice(request, env) {\n  try {\n    const url = new URL(request.url);\n    const encodedData = url.searchParams.get('data');\n\n    if (!encodedData) {\n      return new Response('缺少出貨單資料', { status: 400 });\n    }\n\n    const data = JSON.parse(decodeURIComponent(encodedData));\n\n    // 生成出貨單 HTML\n    const html = generateInvoiceHTML(data);\n\n    return new Response(html, {\n      status: 200,\n      headers: {\n        'Content-Type': 'text/html; charset=utf-8'\n      }\n    });\n\n  } catch (error) {\n    return new Response('解析出貨單資料時發生錯誤: ' + error.message, { status: 500 });\n  }\n}\n\n// 生成出貨單 HTML\nfunction generateInvoiceHTML(data) {\n  const invoiceNumber = 'INV' + new Date(data.invoiceDate).getFullYear() +\n    String(new Date(data.invoiceDate).getMonth() + 1).padStart(2, '0') +\n    String(new Date(data.invoiceDate).getDate()).padStart(2, '0') +\n    String(Math.floor(Math.random() * 1000)).padStart(3, '0');\n\n  // 根據公司 ID 設定供應商資訊\n  let supplierInfo = {\n    name: '您的公司名稱',\n    phone: '06-2906741 / 0980347570',\n    address: '台南市東區崇學路165號5樓'\n  };\n\n  if (data.companyId === 'liang-xin') {\n    supplierInfo = {\n      name: '量心醫藥股份有限公司',\n      phone: '06-2906741 / 0980347570',\n      address: '台南市東區崇學路165號5樓'\n    };\n  } else if (data.companyId === 'jia-xuan') {\n    supplierInfo = {\n      name: '嘉萱漢方有限公司',\n      phone: '06-2906741 / 0980347570',\n      address: '台南市東區崇學路165號5樓'\n    };\n  }\n\n  return `\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>出貨單 - ${data.clientName}</title>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'Microsoft YaHei', '微軟正黑體', Arial, sans-serif;\n            background: #f5f7fa;\n            padding: 20px;\n            color: #333;\n          }\n\n          .invoice-container {\n            max-width: 800px;\n            margin: 0 auto;\n            background: white;\n            box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n            border-radius: 8px;\n            overflow: hidden;\n          }\n\n          .invoice-header {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            padding: 30px;\n            text-align: center;\n            position: relative;\n          }\n\n          .invoice-title {\n            font-size: 32px;\n            font-weight: bold;\n            margin-bottom: 10px;\n            letter-spacing: 2px;\n          }\n\n          .invoice-number {\n            font-size: 14px;\n            opacity: 0.9;\n            position: absolute;\n            top: 15px;\n            right: 30px;\n          }\n\n          .invoice-body {\n            padding: 40px;\n          }\n\n          .invoice-info {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 40px;\n            margin-bottom: 40px;\n            padding-bottom: 30px;\n            border-bottom: 2px solid #f1f3f4;\n          }\n\n          .info-section h3 {\n            color: #667eea;\n            font-size: 16px;\n            margin-bottom: 15px;\n            border-bottom: 1px solid #e9ecef;\n            padding-bottom: 5px;\n          }\n\n          .info-item {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 8px;\n            padding: 5px 0;\n          }\n\n          .info-label {\n            color: #666;\n            font-weight: 500;\n          }\n\n          .info-value {\n            font-weight: 600;\n            color: #333;\n          }\n\n          .items-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 30px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.05);\n            border-radius: 8px;\n            overflow: hidden;\n          }\n\n          .items-table th {\n            background: #f8f9fa;\n            color: #495057;\n            font-weight: 600;\n            padding: 15px 20px;\n            text-align: left;\n            border-bottom: 2px solid #dee2e6;\n          }\n\n          .items-table td {\n            padding: 15px 20px;\n            border-bottom: 1px solid #e9ecef;\n            vertical-align: middle;\n          }\n\n          .items-table tbody tr:hover {\n            background-color: #f8f9fa;\n          }\n\n          .items-table tbody tr:last-child td {\n            border-bottom: none;\n          }\n\n          .text-right {\n            text-align: right;\n          }\n\n          .text-center {\n            text-align: center;\n          }\n\n          .amount {\n            font-weight: 600;\n            font-family: 'Courier New', monospace;\n          }\n\n          .summary {\n            background: #f8f9fa;\n            border-radius: 8px;\n            padding: 25px;\n            margin-bottom: 30px;\n            border-left: 4px solid #667eea;\n          }\n\n          .summary-row {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            margin-bottom: 12px;\n            font-size: 16px;\n          }\n\n          .summary-row.total {\n            font-size: 20px;\n            font-weight: bold;\n            color: #667eea;\n            border-top: 2px solid #dee2e6;\n            padding-top: 15px;\n            margin-top: 20px;\n            margin-bottom: 0;\n          }\n\n          .notes {\n            background: #fff3cd;\n            border: 1px solid #ffeaa7;\n            border-radius: 8px;\n            padding: 20px;\n            margin-bottom: 30px;\n          }\n\n          .notes h4 {\n            color: #856404;\n            margin-bottom: 10px;\n          }\n\n          .notes p {\n            color: #856404;\n            line-height: 1.5;\n            margin: 0;\n          }\n\n          .footer {\n            text-align: center;\n            color: #6c757d;\n            font-size: 14px;\n            padding: 20px;\n            border-top: 1px solid #e9ecef;\n            background: #f8f9fa;\n          }\n\n          .actions {\n            text-align: center;\n            padding: 20px;\n            background: #f8f9fa;\n            border-top: 1px solid #e9ecef;\n          }\n\n          .btn {\n            display: inline-block;\n            padding: 12px 24px;\n            margin: 0 10px;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 16px;\n            font-weight: 500;\n            text-decoration: none;\n            transition: all 0.3s ease;\n          }\n\n          .btn-primary {\n            background: #667eea;\n            color: white;\n          }\n\n          .btn-primary:hover {\n            background: #5a67d8;\n            transform: translateY(-2px);\n            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n          }\n\n          .btn-secondary {\n            background: #6c757d;\n            color: white;\n          }\n\n          .btn-secondary:hover {\n            background: #5a6268;\n            transform: translateY(-2px);\n          }\n\n          @media print {\n            body {\n              background: white;\n              padding: 0;\n            }\n\n            .invoice-container {\n              box-shadow: none;\n              border-radius: 0;\n            }\n\n            .actions, .btn {\n              display: none !important;\n            }\n\n            .invoice-header {\n              background: #667eea !important;\n              -webkit-print-color-adjust: exact;\n              color-adjust: exact;\n            }\n\n            .summary {\n              background: #f8f9fa !important;\n              -webkit-print-color-adjust: exact;\n              color-adjust: exact;\n            }\n          }\n\n          @media (max-width: 768px) {\n            .invoice-info {\n              grid-template-columns: 1fr;\n              gap: 20px;\n            }\n\n            .items-table th,\n            .items-table td {\n              padding: 10px 15px;\n            }\n\n            .invoice-title {\n              font-size: 24px;\n            }\n\n            .btn {\n              display: block;\n              margin: 10px auto;\n              width: 200px;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"invoice-container\">\n          <div class=\"invoice-header\">\n            <div class=\"invoice-number\">單號: ${invoiceNumber}</div>\n            <div class=\"invoice-title\">出 貨 單</div>\n            <div>DELIVERY NOTE</div>\n          </div>\n\n          <div class=\"invoice-body\">\n            <div class=\"invoice-info\">\n              <div class=\"info-section\">\n                <h3>客戶資訊</h3>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">客戶名稱：</span>\n                  <span class=\"info-value\">${data.clientName}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">出貨日期：</span>\n                  <span class=\"info-value\">${new Date(data.invoiceDate).toLocaleDateString('zh-TW')}</span>\n                </div>\n              </div>\n\n              <div class=\"info-section\">\n                <h3>供應商資訊</h3>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">公司名稱：</span>\n                  <span class=\"info-value\">${supplierInfo.name}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">聯絡電話：</span>\n                  <span class=\"info-value\">${supplierInfo.phone}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">地址：</span>\n                  <span class=\"info-value\">${supplierInfo.address}</span>\n                </div>\n              </div>\n            </div>\n\n            <table class=\"items-table\">\n              <thead>\n                <tr>\n                  <th style=\"width: 50px;\">#</th>\n                  <th>商品名稱</th>\n                  <th class=\"text-center\" style=\"width: 100px;\">數量</th>\n                  <th class=\"text-right\" style=\"width: 120px;\">單價</th>\n                  <th class=\"text-right\" style=\"width: 120px;\">金額</th>\n                </tr>\n              </thead>\n              <tbody>\n                ${data.items.map((item, index) => `\n                  <tr>\n                    <td class=\"text-center\">${index + 1}</td>\n                    <td>${item.name}</td>\n                    <td class=\"text-center\">${item.quantity}</td>\n                    <td class=\"text-right amount\">$${item.unitPrice.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</td>\n                    <td class=\"text-right amount\">$${item.amount.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</td>\n                  </tr>\n                `).join('')}\n              </tbody>\n            </table>\n\n            <div class=\"summary\">\n              <div class=\"summary-row\">\n                <span>商品小計：</span>\n                <span class=\"amount\">$${data.subtotal.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</span>\n              </div>\n              <div class=\"summary-row\">\n                <span>運費：</span>\n                <span class=\"amount\">$${data.shippingFee.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</span>\n              </div>\n              <div class=\"summary-row total\">\n                <span>本次應付總計：</span>\n                <span class=\"amount\">$${data.total.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</span>\n              </div>\n            </div>\n\n            <div class=\"summary\" style=\"background: #f0f8ff; border-left-color: #007bff;\">\n              <h4 style=\"color: #007bff; margin-bottom: 15px;\">帳務計算</h4>\n              <div class=\"summary-row\">\n                <span>前期餘額：</span>\n                <span class=\"amount\">$${(data.previousBalance || 0).toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</span>\n              </div>\n              <div class=\"summary-row\">\n                <span>減：本次應付：</span>\n                <span class=\"amount\">$${data.total.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}</span>\n              </div>\n              <div class=\"summary-row total\" style=\"background: ${data.balance === 0 ? '#d4edda' : data.balance < 0 ? '#f8d7da' : '#fff3cd'}; padding: 10px; border-radius: 5px;\">\n                <span style=\"color: ${data.balance === 0 ? '#155724' : data.balance < 0 ? '#721c24' : '#856404'};\">\n                  ${data.balance === 0 ? '✓ 已結清' : data.balance < 0 ? '客戶欠款：' : '客戶餘額：'}\n                </span>\n                <span class=\"amount\" style=\"color: ${data.balance === 0 ? '#155724' : data.balance < 0 ? '#721c24' : '#856404'}; font-size: 22px;\">\n                  $${data.balance < 0 ? data.balance.toLocaleString('zh-TW', { minimumFractionDigits: 2 }) : (data.balance || 0).toLocaleString('zh-TW', { minimumFractionDigits: 2 })}\n                </span>\n              </div>\n            </div>\n\n            ${data.notes ? `\n              <div class=\"notes\">\n                <h4>📝 備註</h4>\n                <p>${data.notes}</p>\n              </div>\n            ` : ''}\n          </div>\n\n          <div class=\"actions\">\n            <button class=\"btn btn-primary\" onclick=\"window.print()\">🖨️ 列印出貨單</button>\n            <button class=\"btn btn-secondary\" onclick=\"window.close()\">✕ 關閉視窗</button>\n          </div>\n\n          <div class=\"footer\">\n            <p>此出貨單由系統自動生成 | 生成時間：${new Date().toLocaleString('zh-TW')}</p>\n          </div>\n        </div>\n      </body>\n    </html>\n  `;\n}\n\n// 生成包含嵌入式 Google Sheets 的 HTML 頁面\nfunction getEmbeddedSheetPage(fileId, clientName) {\n  // Google Sheets 嵌入式編輯器 URL - 使用編輯模式參數\n  // rm=minimal 移除大部分 UI，widget=false 隱藏小工具，headers=false 隱藏標題\n  const embedUrl = `https://docs.google.com/spreadsheets/d/${fileId}/edit?rm=minimal&headers=false&widget=false`;\n\n  return `\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>${clientName ? clientName + ' - ' : ''}Google Sheets 編輯器</title>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            background: #f5f5f5;\n            display: flex;\n            flex-direction: column;\n            height: 100vh;\n            overflow: hidden;\n          }\n\n          .header {\n            background: #1a73e8;\n            color: white;\n            padding: 15px 20px;\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n          }\n\n          .header h1 {\n            font-size: 20px;\n            font-weight: 500;\n            display: flex;\n            align-items: center;\n            gap: 10px;\n          }\n\n          .client-name {\n            background: rgba(255,255,255,0.2);\n            padding: 5px 12px;\n            border-radius: 20px;\n            font-size: 14px;\n          }\n\n          .actions {\n            display: flex;\n            gap: 10px;\n          }\n\n          .btn {\n            background: white;\n            color: #1a73e8;\n            border: none;\n            padding: 8px 16px;\n            border-radius: 4px;\n            cursor: pointer;\n            font-size: 14px;\n            font-weight: 500;\n            text-decoration: none;\n            display: inline-flex;\n            align-items: center;\n            gap: 5px;\n            transition: all 0.3s ease;\n          }\n\n          .btn:hover {\n            box-shadow: 0 2px 8px rgba(0,0,0,0.15);\n            transform: translateY(-1px);\n          }\n\n          .iframe-container {\n            flex: 1;\n            position: relative;\n            background: white;\n            margin: 20px;\n            border-radius: 8px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n            overflow: hidden;\n          }\n\n          iframe {\n            width: 100%;\n            height: 100%;\n            border: none;\n          }\n\n          .loading {\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            text-align: center;\n            color: #666;\n          }\n\n          .spinner {\n            border: 3px solid #f3f3f3;\n            border-top: 3px solid #1a73e8;\n            border-radius: 50%;\n            width: 40px;\n            height: 40px;\n            animation: spin 1s linear infinite;\n            margin: 0 auto 20px;\n          }\n\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n\n          @media (max-width: 768px) {\n            .header {\n              flex-direction: column;\n              gap: 10px;\n              text-align: center;\n            }\n\n            .iframe-container {\n              margin: 10px;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"header\">\n          <h1>\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n              <polyline points=\"14 2 14 8 20 8\"></polyline>\n              <line x1=\"8\" y1=\"13\" x2=\"16\" y2=\"13\"></line>\n              <line x1=\"8\" y1=\"17\" x2=\"16\" y2=\"17\"></line>\n            </svg>\n            Google Sheets 編輯器\n            ${clientName ? `<span class=\"client-name\">${clientName}</span>` : ''}\n          </h1>\n          <div class=\"actions\">\n            <a href=\"https://docs.google.com/spreadsheets/d/${fileId}/edit\" target=\"_blank\" class=\"btn\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n                <polyline points=\"15 3 21 3 21 9\"></polyline>\n                <line x1=\"10\" y1=\"14\" x2=\"21\" y2=\"3\"></line>\n              </svg>\n              在新視窗開啟\n            </a>\n            <button onclick=\"location.reload()\" class=\"btn\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <polyline points=\"23 4 23 10 17 10\"></polyline>\n                <polyline points=\"1 20 1 14 7 14\"></polyline>\n                <path d=\"M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15\"></path>\n              </svg>\n              重新整理\n            </button>\n          </div>\n        </div>\n\n        <div class=\"iframe-container\">\n          <div class=\"loading\" id=\"loading\">\n            <div class=\"spinner\"></div>\n            <p>正在載入 Google Sheets...</p>\n          </div>\n          <iframe\n            id=\"sheet-iframe\"\n            src=\"${embedUrl}\"\n            onload=\"document.getElementById('loading').style.display='none'\"\n            onerror=\"handleError()\"\n            allowfullscreen>\n          </iframe>\n        </div>\n\n        <script>\n          function handleError() {\n            document.getElementById('loading').innerHTML =\n              '<p style=\"color: #d93025;\">無法載入 Google Sheets</p>' +\n              '<p style=\"margin-top: 10px; font-size: 14px;\">請確認您有存取權限</p>' +\n              '<button onclick=\"location.reload()\" class=\"btn\" style=\"margin-top: 20px;\">重試</button>';\n          }\n\n          // 超時處理\n          setTimeout(function() {\n            const loading = document.getElementById('loading');\n            if (loading && loading.style.display !== 'none') {\n              loading.innerHTML =\n                '<p style=\"color: #ff9800;\">載入時間較長...</p>' +\n                '<p style=\"margin-top: 10px; font-size: 14px;\">如果無法載入，請檢查您的網路連線</p>';\n            }\n          }, 10000);\n        </script>\n      </body>\n    </html>\n  `;\n}", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTH<PERSON>_EXPORTS from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/src/index.js\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/src/index.js\";\n\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/.wrangler/tmp/bundle-vFfCd4/middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/node_modules/wrangler/templates/middleware/common.ts\";\nimport type { WorkerEntrypointConstructor } from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/.wrangler/tmp/bundle-vFfCd4/middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"/Users/<USER>/Library/CloudStorage/Dropbox/Code/Google/出貨單/.wrangler/tmp/bundle-vFfCd4/middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,SAAS,0BAA0B,OAAO,MAAM;AAC/C,QAAM,UAAU,IAAI,QAAQ,OAAO,IAAI;AACvC,UAAQ,QAAQ,OAAO,kBAAkB;AACzC,SAAO;AACR;AAJS;AAMT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,WAAO,QAAQ,MAAM,QAAQ,SAAS;AAAA,MACrC,0BAA0B,MAAM,MAAM,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACF;AACD,CAAC;;;ACZD;AAAA,EACE,WAAa;AAAA,IACX;AAAA,MACE,IAAM;AAAA,MACN,MAAQ;AAAA,MACR,SAAW;AAAA,QACT;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,IAAM;AAAA,MACN,MAAQ;AAAA,MACR,SAAW;AAAA,QACT;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA;AAAA,UACE,IAAM;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACrFA,IAAO,cAAQ;AAAA,EACb,MAAM,MAAM,SAAS,KAAK,KAAK;AAC7B,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,OAAO,IAAI;AAGjB,QAAI,SAAS,OAAO,SAAS,IAAI;AAC/B,aAAO,IAAI,SAAS,YAAY,GAAG;AAAA,QACjC,SAAS,EAAE,gBAAgB,YAAY;AAAA,MACzC,CAAC;AAAA,IACH;AAGA,QAAI,SAAS,mBAAmB;AAC9B,aAAO,oBAAoB,SAAS,GAAG;AAAA,IACzC;AAGA,QAAI,SAAS,SAAS;AACpB,aAAO,WAAW,GAAG;AAAA,IACvB;AAGA,QAAI,SAAS,eAAe;AAC1B,aAAO,gBAAgB,SAAS,GAAG;AAAA,IACrC;AAGA,QAAI,SAAS,YAAY;AACvB,aAAO,mBAAmB,SAAS,GAAG;AAAA,IACxC;AAGA,QAAI,SAAS,kBAAkB;AAC7B,aAAO,mBAAmB,SAAS,GAAG;AAAA,IACxC;AAGA,QAAI,SAAS,mBAAmB;AAC9B,aAAO,oBAAoB,SAAS,GAAG;AAAA,IACzC;AAGA,QAAI,SAAS,mBAAmB;AAC9B,aAAO,oBAAoB,SAAS,GAAG;AAAA,IACzC;AAGA,QAAI,SAAS,oBAAoB;AAC/B,aAAO,qBAAqB,SAAS,GAAG;AAAA,IAC1C;AAGA,QAAI,SAAS,yBAAyB;AACpC,aAAO,qBAAqB,SAAS,GAAG;AAAA,IAC1C;AAGA,QAAI,SAAS,qBAAqB;AAChC,aAAO,2BAA2B,SAAS,GAAG;AAAA,IAChD;AAGA,WAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC;AAAA,EAClD;AACF;AAGA,SAAS,cAAc;AACrB,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiDT;AAlDS;AAqDT,eAAe,WAAW,KAAK;AAC7B,QAAM,WAAW,IAAI;AACrB,QAAM,cAAc,IAAI;AAGxB,QAAM,SAAS;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE,KAAK,GAAG;AAEV,QAAM,UAAU,IAAI,IAAI,2CAA2C;AACnE,UAAQ,aAAa,IAAI,aAAa,QAAQ;AAC9C,UAAQ,aAAa,IAAI,gBAAgB,WAAW;AACpD,UAAQ,aAAa,IAAI,iBAAiB,MAAM;AAChD,UAAQ,aAAa,IAAI,SAAS,MAAM;AACxC,UAAQ,aAAa,IAAI,eAAe,SAAS;AACjD,UAAQ,aAAa,IAAI,UAAU,SAAS;AAE5C,SAAO,SAAS,SAAS,QAAQ,SAAS,GAAG,GAAG;AAClD;AApBe;AAuBf,eAAe,oBAAoB,SAAS,KAAK;AAC/C,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,QAAM,OAAO,IAAI,aAAa,IAAI,MAAM;AACxC,QAAM,QAAQ,IAAI,aAAa,IAAI,OAAO;AAE1C,MAAI,OAAO;AACT,WAAO,IAAI,SAAS,kCAAc,SAAS,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC5D;AAEA,MAAI,CAAC,MAAM;AACT,WAAO,IAAI,SAAS,wCAAU,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC/C;AAEA,MAAI;AAEF,UAAM,gBAAgB,MAAM,MAAM,uCAAuC;AAAA,MACvE,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM,IAAI,gBAAgB;AAAA,QACxB,WAAW,IAAI;AAAA,QACf,eAAe,IAAI;AAAA,QACnB;AAAA,QACA,YAAY;AAAA,QACZ,cAAc,IAAI;AAAA,MACpB,CAAC;AAAA,IACH,CAAC;AAED,QAAI,CAAC,cAAc,IAAI;AACrB,YAAM,YAAY,MAAM,cAAc,KAAK;AAC3C,aAAO,IAAI,SAAS,yCAAW,aAAa,EAAE,QAAQ,IAAI,CAAC;AAAA,IAC7D;AAEA,UAAM,SAAS,MAAM,cAAc,KAAK;AAGxC,UAAM,WAAW,IAAI,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0EAgBA,OAAO,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oDAQH,OAAO;AAAA,kEACO,OAAO,cAAc,QAAQ;AAAA,mBAC5E,OAAO;AAAA,uDAC6B,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,OAKvD;AAAA,MACD,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,cAAc;AAAA,UACZ,gBAAgB,OAAO,iCAAiC,OAAO,cAAc;AAAA,UAC7E,cAAc,OAAO,cAAc,6BAA6B,OAAO,cAAc;AAAA,QACvF;AAAA,MACF;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT,SAASA,QAAP;AACA,WAAO,IAAI,SAAS,kCAAcA,OAAM,WAAW,EAAE,QAAQ,IAAI,CAAC;AAAA,EACpE;AACF;AAnFe;AAsFf,eAAe,gBAAgB,SAAS,KAAK;AAG3C,SAAO,IAAI,SAAS,gEAAc,EAAE,QAAQ,IAAI,CAAC;AACnD;AAJe;AAOf,eAAe,mBAAmB,SAAS,KAAK;AAC9C,MAAI;AACF,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,SAAS,IAAI,aAAa,IAAI,QAAQ;AAC5C,UAAM,aAAa,IAAI,aAAa,IAAI,YAAY;AAEpD,QAAI,CAAC,QAAQ;AACX,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,mCAAe,CAAC,GAAG;AAAA,QAC7D,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAGA,UAAM,OAAO,qBAAqB,QAAQ,UAAU;AAEpD,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EAEH,SAAS,OAAP;AACA,YAAQ,MAAM,sEAAoB,KAAK;AACvC,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,OAAO;AAAA,MACP,SAAS,MAAM;AAAA,IACjB,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AACF;AAjCe;AA+Cf,eAAe,mBAAmB,SAAS,KAAK;AAG9C,QAAM,eAAe,CAAC;AACtB,oBAAc,UAAU,QAAQ,aAAW;AACzC,YAAQ,QAAQ,QAAQ,YAAU;AAChC,mBAAa,KAAK;AAAA,QAChB,SAAS,QAAQ;AAAA,QACjB,MAAM,OAAO;AAAA,QACb,IAAI,OAAO;AAAA,QACX,QAAQ,OAAO,UAAU;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YA+QH,kBAAc,UAAU,IAAI,aAAW;AAAA;AAAA,0CAET,QAAQ;AAAA;AAAA,kBAEhC,QAAQ,QAAQ,IAAI,YAAU;AAC5C,WAAO;AAAA;AAAA,iDAEsC,OAAO;AAAA;AAAA,wCAE1B,OAAO,UAAU;AAAA;AAAA;AAAA,iDAGE,OAAO,qBAAqB,mBAAmB,OAAO,IAAI;AAAA;AAAA;AAAA;AAAA,wDAInD,OAAO,qBAAqB,mBAAmB,OAAO,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhH,CAAC,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA,WAGD,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgDlB,SAAO,IAAI,SAAS,MAAM;AAAA,IACxB,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AA9We;AAiXf,eAAe,oBAAoB,SAAS,KAAK;AAC/C,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,QAAM,aAAa,IAAI,aAAa,IAAI,YAAY,KAAK;AACzD,QAAM,SAAS,IAAI,aAAa,IAAI,QAAQ,KAAK,IAAI,mBAAmB;AAGxE,MAAI,YAAY;AAChB,MAAI,cAAc;AAClB,MAAI,aAAa;AAEjB,aAAW,WAAW,kBAAc,WAAW;AAC7C,UAAM,SAAS,QAAQ,QAAQ,KAAK,OAAK,EAAE,SAAS,UAAU;AAC9D,QAAI,QAAQ;AACV,kBAAY,QAAQ;AACpB,oBAAc,QAAQ;AACtB,mBAAa,OAAO;AACpB;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA,wDAIW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mCA2JN,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,0DAKuB;AAAA,8DACI;AAAA,6DACD;AAAA,+DACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCAkP7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAmDE,KAAK,UAAU,iBAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgL9D,SAAO,IAAI,SAAS,MAAM;AAAA,IACxB,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AAtpBe;AAypBf,eAAe,oBAAoB,SAAS,KAAK;AAC/C,MAAI,QAAQ,WAAW,QAAQ;AAC7B,WAAO,IAAI,SAAS,sBAAsB,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC3D;AAEA,MAAI;AACF,UAAM,OAAO,MAAM,QAAQ,KAAK;AAGhC,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,QAAQ;AACX,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAGA,UAAM,cAAc,eAAe,OAAO;AAE1C,QAAI;AAEF,YAAM,SAAS;AAAA,QACb,KAAK;AAAA;AAAA,QACL,KAAK,MAAM,IAAI,UAAQ,GAAG,KAAK,SAAS,KAAK,UAAU,EAAE,KAAK,IAAI;AAAA;AAAA,QAClE,KAAK,MAAM,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,UAAU,CAAC;AAAA;AAAA,QACvD,KAAK;AAAA;AAAA,QACL,KAAK;AAAA;AAAA,QACL,KAAK;AAAA;AAAA,MACP;AAEA,UAAI,eAAe;AAGnB,UAAI,IAAI,kBAAkB,CAAC,cAAc;AACvC,YAAI;AACF,gBAAM,YAAY,iDAAiD,8DAA8D,IAAI;AACrI,gBAAM,WAAW,MAAM,MAAM,WAAW;AAAA,YACtC,QAAQ;AAAA,YACR,SAAS;AAAA,cACP,gBAAgB;AAAA,YAClB;AAAA,YACA,MAAM,KAAK,UAAU;AAAA,cACnB,OAAO;AAAA,cACP,gBAAgB;AAAA,cAChB,QAAQ,CAAC,MAAM;AAAA,YACjB,CAAC;AAAA,UACH,CAAC;AAED,cAAI,SAAS,IAAI;AACf,2BAAe;AACf,oBAAQ,IAAI,+CAAiB;AAAA,UAC/B,OAAO;AACL,oBAAQ,MAAM,qCAAiB,MAAM,SAAS,KAAK,CAAC;AAAA,UACtD;AAAA,QACF,SAAS,OAAP;AACA,kBAAQ,MAAM,qCAAiB,KAAK;AAAA,QACtC;AAAA,MACF;AAGA,UAAI,eAAe,CAAC,cAAc;AAChC,YAAI;AACF,gBAAM,YAAY,iDAAiD;AACnE,gBAAM,WAAW,MAAM,MAAM,WAAW;AAAA,YACtC,QAAQ;AAAA,YACR,SAAS;AAAA,cACP,iBAAiB,UAAU;AAAA,cAC3B,gBAAgB;AAAA,YAClB;AAAA,YACA,MAAM,KAAK,UAAU;AAAA,cACnB,OAAO;AAAA,cACP,gBAAgB;AAAA,cAChB,QAAQ,CAAC,MAAM;AAAA,YACjB,CAAC;AAAA,UACH,CAAC;AAED,cAAI,SAAS,IAAI;AACf,2BAAe;AACf,oBAAQ,IAAI,6CAAe;AAAA,UAC7B,OAAO;AACL,oBAAQ,MAAM,mCAAe,MAAM,SAAS,KAAK,CAAC;AAAA,UACpD;AAAA,QACF,SAAS,OAAP;AACA,kBAAQ,MAAM,mCAAe,KAAK;AAAA,QACpC;AAAA,MACF;AAEA,UAAI,cAAc;AAChB,eAAO,IAAI,SAAS,KAAK,UAAU;AAAA,UACjC,SAAS;AAAA,UACT,SAAS;AAAA,UACT;AAAA,QACF,CAAC,GAAG;AAAA,UACF,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH,OAAO;AACL,eAAO,IAAI,SAAS,KAAK,UAAU;AAAA,UACjC,SAAS;AAAA,UACT,OAAO;AAAA,QACT,CAAC,GAAG;AAAA,UACF,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH;AAAA,IAEF,SAAS,YAAP;AACA,cAAQ,MAAM,4CAAwB,UAAU;AAChD,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO,+BAAW,WAAW;AAAA,MAC/B,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EAEF,SAAS,OAAP;AACA,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACf,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AApIe;AAwIf,SAAS,eAAe,SAAS;AAE/B,QAAM,eAAe,QAAQ,QAAQ,IAAI,QAAQ;AACjD,MAAI,cAAc;AAChB,UAAM,UAAU,aAAa,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,WAAW;AAC9D,YAAM,CAAC,KAAK,KAAK,IAAI,OAAO,KAAK,EAAE,MAAM,GAAG;AAC5C,UAAI,GAAG,IAAI;AACX,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,QAAI,QAAQ,cAAc;AACxB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AAGA,QAAM,aAAa,QAAQ,QAAQ,IAAI,eAAe;AACtD,MAAI,cAAc,WAAW,WAAW,SAAS,GAAG;AAClD,WAAO,WAAW,MAAM,CAAC;AAAA,EAC3B;AAEA,SAAO;AACT;AArBS;AAuBT,eAAe,qBAAqB,SAAS,KAAK;AAChD,MAAI;AACF,UAAM,cAAc,MAAM,QAAQ,KAAK;AACvC,UAAM,EAAE,QAAQ,WAAW,IAAI;AAE/B,QAAI,CAAC,QAAQ;AACX,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,mCAAU,CAAC,GAAG;AAAA,QACxD,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAGA,UAAM,cAAc,eAAe,OAAO;AAG1C,QAAI,IAAI,gBAAgB;AACtB,UAAI;AACF,cAAM,SAAS,iDAAiD,yBAAyB,IAAI;AAC7F,gBAAQ,IAAI,mEAAgC,MAAM;AAElD,cAAM,WAAW,MAAM,MAAM,QAAQ;AAAA,UACnC,SAAS;AAAA,YACP,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,YAAI,SAAS,IAAI;AACf,gBAAM,OAAO,MAAM,SAAS,KAAK;AACjC,cAAI,cAAc;AAElB,cAAI,KAAK,UAAU,KAAK,OAAO,SAAS,GAAG;AACzC,qBAAS,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,oBAAM,MAAM,KAAK,OAAO,CAAC;AACzB,kBAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,WAAW,IAAI,CAAC,CAAC,CAAC,GAAG;AAC/C,8BAAc,WAAW,IAAI,CAAC,CAAC;AAC/B,wBAAQ,IAAI,6DAAqB,WAAW;AAC5C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,iBAAO,IAAI,SAAS,KAAK,UAAU;AAAA,YACjC;AAAA,YACA,MAAM;AAAA,UACR,CAAC,GAAG;AAAA,YACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,YAAY,MAAM,SAAS,KAAK;AACtC,kBAAQ,MAAM,qCAAiB,SAAS,QAAQ,SAAS;AAAA,QAC3D;AAAA,MACF,SAAS,OAAP;AACA,gBAAQ,MAAM,qCAAiB,KAAK;AAAA,MACtC;AAAA,IACF;AAEA,QAAI,CAAC,aAAa;AAEhB,YAAM,uBAAuB;AAAA,QAC3B,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,QAChD,gDAAgD;AAAA;AAAA,MAClD;AAEA,YAAM,cAAc,qBAAqB,MAAM,KAAK;AACpD,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC;AAAA,QACA,MAAM;AAAA,MACR,CAAC,GAAG;AAAA,QACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAGA,QAAI,aAAa;AACf,UAAI;AAEF,cAAM,cAAc,iDAAiD;AAErE,gBAAQ,IAAI,qDAAa,WAAW;AACpC,gBAAQ,IAAI,6BAAmB,YAAY,UAAU,GAAG,EAAE,IAAI,KAAK;AAEnE,cAAM,mBAAmB,MAAM,MAAM,aAAa;AAAA,UAChD,SAAS;AAAA,YACP,iBAAiB,UAAU;AAAA,YAC3B,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,YAAI,CAAC,iBAAiB,IAAI;AACxB,gBAAM,YAAY,MAAM,iBAAiB,KAAK;AAC9C,kBAAQ,MAAM,iDAAmB,SAAS;AAG1C,gBAAM,cAAc,iDAAiD;AACrE,kBAAQ,IAAI,6BAAmB,WAAW;AAAA,QAC5C,OAAO;AACL,gBAAM,WAAW,MAAM,iBAAiB,KAAK;AAC7C,kBAAQ,IAAI,6BAAS;AAAA,YACnB,OAAO,SAAS,YAAY;AAAA,YAC5B,QAAQ,SAAS,QAAQ,IAAI,OAAK,EAAE,YAAY,KAAK;AAAA,UACvD,CAAC;AAAA,QACH;AAGA,YAAI,SAAS,iDAAiD;AAG9D,YAAI,iBAAiB,IAAI;AACvB,gBAAM,WAAW,MAAM,iBAAiB,KAAK;AAC7C,cAAI,SAAS,UAAU,SAAS,OAAO,SAAS,GAAG;AACjD,kBAAM,iBAAiB,SAAS,OAAO,CAAC,EAAE,WAAW;AACrD,qBAAS,iDAAiD,kBAAkB;AAC5E,oBAAQ,IAAI,+CAAY,cAAc;AAAA,UACxC;AAAA,QACF;AAEA,gBAAQ,IAAI,8CAA0B,MAAM;AAG5C,YAAI,WAAW,MAAM,MAAM,QAAQ;AAAA,UACjC,SAAS;AAAA,YACP,iBAAiB,UAAU;AAAA,YAC3B,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAGD,YAAI,CAAC,SAAS,IAAI;AAChB,kBAAQ,IAAI,oEAAuB;AACnC,gBAAM,YAAY,GAAG,cAAc,IAAI;AACvC,kBAAQ,IAAI,4BAAkB,SAAS;AAEvC,qBAAW,MAAM,MAAM,WAAW;AAAA,YAChC,SAAS;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF,CAAC;AAGD,cAAI,CAAC,SAAS,IAAI;AAChB,oBAAQ,IAAI,wEAAsB;AAClC,kBAAM,SAAS,0CAA0C;AACzD,oBAAQ,IAAI,wBAAc,MAAM;AAEhC,kBAAM,cAAc,MAAM,MAAM,MAAM;AACtC,gBAAI,YAAY,IAAI;AAClB,oBAAM,UAAU,MAAM,YAAY,KAAK;AACvC,sBAAQ,IAAI,gCAAY,QAAQ,MAAM;AAGtC,oBAAM,QAAQ,QAAQ,MAAM,IAAI,EAAE,OAAO,UAAQ,KAAK,KAAK,CAAC;AAC5D,uBAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,sBAAM,OAAO,MAAM,CAAC,EAAE,MAAM,GAAG;AAC/B,oBAAI,KAAK,CAAC,KAAK,CAAC,MAAM,WAAW,KAAK,CAAC,CAAC,CAAC,GAAG;AAC1C,wBAAM,UAAU,WAAW,KAAK,CAAC,CAAC;AAClC,0BAAQ,IAAI,yDAAiB,OAAO;AACpC,yBAAO,IAAI,SAAS,KAAK,UAAU;AAAA,oBACjC,aAAa;AAAA,oBACb,MAAM;AAAA,kBACR,CAAC,GAAG;AAAA,oBACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,kBAChD,CAAC;AAAA,gBACH;AAAA,cACF;AAAA,YACF;AAGA,uBAAW;AAAA,UACb;AAAA,QACF;AAEA,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,YAAY,MAAM,SAAS,KAAK;AACtC,kBAAQ,MAAM,kCAAwB,SAAS;AAC/C,kBAAQ,MAAM,oBAAoB,SAAS,QAAQ,SAAS,UAAU;AAGtE,cAAI,YAAY;AAChB,cAAI,SAAS,WAAW,KAAK;AAC3B,wBAAY;AAAA,UACd,WAAW,SAAS,WAAW,KAAK;AAClC,wBAAY;AAAA,UACd,WAAW,SAAS,WAAW,KAAK;AAClC,wBAAY;AAAA,UACd,OAAO;AACL,wBAAY,oCAAW,SAAS,UAAU,SAAS;AAAA,UACrD;AAGA,gBAAM,uBAAuB;AAAA,YAC3B,gDAAgD;AAAA,YAChD,gDAAgD;AAAA,YAChD,gDAAgD;AAAA,UAClD;AAEA,gBAAMC,eAAc,qBAAqB,MAAM,KAAK;AACpD,iBAAO,IAAI,SAAS,KAAK,UAAU;AAAA,YACjC,aAAAA;AAAA,YACA,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC,GAAG;AAAA,YACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD,CAAC;AAAA,QACH;AAEA,cAAM,OAAO,MAAM,SAAS,KAAK;AACjC,YAAI,cAAc;AAGlB,YAAI,KAAK,UAAU,KAAK,OAAO,SAAS,GAAG;AACzC,mBAAS,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,kBAAM,MAAM,KAAK,OAAO,CAAC;AACzB,gBAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,WAAW,IAAI,CAAC,CAAC,CAAC,GAAG;AAC/C,4BAAc,WAAW,IAAI,CAAC,CAAC;AAC/B,sBAAQ,IAAI,yDAAiB,WAAW;AACxC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,eAAO,IAAI,SAAS,KAAK,UAAU;AAAA,UACjC;AAAA,UACA,MAAM;AAAA,QACR,CAAC,GAAG;AAAA,UACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MAEH,SAAS,UAAP;AACA,gBAAQ,MAAM,8CAA0B,QAAQ;AAGhD,cAAM,uBAAuB;AAAA,UAC3B,gDAAgD;AAAA,UAChD,gDAAgD;AAAA,UAChD,gDAAgD;AAAA,QAClD;AAEA,cAAM,cAAc,qBAAqB,MAAM,KAAK;AACpD,eAAO,IAAI,SAAS,KAAK,UAAU;AAAA,UACjC;AAAA,UACA,MAAM,4FAAsB,SAAS;AAAA,QACvC,CAAC,GAAG;AAAA,UACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EAEF,SAAS,OAAP;AACA,WAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,6DAAgB,MAAM,QAAQ,CAAC,GAAG;AAAA,MAC5E,QAAQ;AAAA,MACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AACF;AA3Qe;AA8Qf,eAAe,2BAA2B,SAAS,KAAK;AACtD,MAAI;AACF,UAAM,cAAc,MAAM,QAAQ,KAAK;AACvC,UAAM,EAAE,OAAO,IAAI;AAEnB,QAAI,CAAC,QAAQ;AACX,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAGA,UAAM,cAAc,eAAe,OAAO;AAC1C,QAAI,WAAW,CAAC;AAGhB,QAAI,IAAI,gBAAgB;AACtB,UAAI;AAEF,cAAM,cAAc,iDAAiD,cAAc,IAAI;AACvF,cAAM,mBAAmB,MAAM,MAAM,aAAa;AAAA,UAChD,SAAS;AAAA,YACP,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,YAAI,mBAAmB;AAEvB,YAAI,iBAAiB,IAAI;AACvB,gBAAM,WAAW,MAAM,iBAAiB,KAAK;AAC7C,kBAAQ,IAAI,mCAAU,SAAS,QAAQ,IAAI,OAAK,EAAE,YAAY,KAAK,CAAC;AAGpE,gBAAM,gBAAgB,CAAC,4BAAQ,gBAAM,4BAAQ,gBAAM,YAAY,SAAS,oBAAK;AAC7E,gBAAM,aAAa,SAAS,QAAQ,IAAI,OAAK,EAAE,YAAY,KAAK,KAAK,CAAC;AAEtE,qBAAW,QAAQ,eAAe;AAChC,gBAAI,WAAW,SAAS,IAAI,GAAG;AAC7B,iCAAmB;AACnB;AAAA,YACF;AAAA,UACF;AAGA,cAAI,CAAC,WAAW,SAAS,gBAAgB,KAAK,WAAW,SAAS,GAAG;AACnE,+BAAmB,WAAW,CAAC;AAAA,UACjC;AAAA,QACF;AAGA,cAAM,SAAS,iDAAiD,kBAAkB,6BAA6B,IAAI;AACnH,gBAAQ,IAAI,yCAAW,MAAM;AAE7B,cAAM,WAAW,MAAM,MAAM,QAAQ;AAAA,UACnC,SAAS;AAAA,YACP,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,YAAI,SAAS,IAAI;AACf,gBAAM,OAAO,MAAM,SAAS,KAAK;AACjC,kBAAQ,IAAI,qDAAa,IAAI;AAE7B,cAAI,KAAK,UAAU,KAAK,OAAO,SAAS,GAAG;AAGzC,qBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,oBAAM,MAAM,KAAK,OAAO,CAAC;AACzB,kBAAI,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAC3B,sBAAM,UAAU;AAAA,kBACd,IAAI,QAAQ,IAAI;AAAA,kBAChB,MAAM,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK;AAAA,kBAC7B,OAAO,WAAW,IAAI,CAAC,CAAC,KAAK;AAAA,kBAC7B,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,IAAI;AAAA,gBAC5C;AAEA,oBAAI,QAAQ,QAAQ,QAAQ,QAAQ,GAAG;AACrC,2BAAS,KAAK,OAAO;AAAA,gBACvB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,kBAAQ,IAAI,qDAAa,QAAQ;AAEjC,iBAAO,IAAI,SAAS,KAAK,UAAU;AAAA,YACjC,SAAS;AAAA,YACT;AAAA,YACA,WAAW;AAAA,YACX,MAAM,iCAAQ;AAAA,UAChB,CAAC,GAAG;AAAA,YACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD,CAAC;AAAA,QACH,OAAO;AACL,kBAAQ,MAAM,qDAAa,MAAM,SAAS,KAAK,CAAC;AAAA,QAClD;AAAA,MACF,SAAS,OAAP;AACA,gBAAQ,MAAM,6DAAqB,KAAK;AAAA,MAC1C;AAAA,IACF;AAGA,QAAI,eAAe,SAAS,WAAW,GAAG;AACxC,UAAI;AAEF,cAAM,cAAc,iDAAiD;AACrE,cAAM,mBAAmB,MAAM,MAAM,aAAa;AAAA,UAChD,SAAS;AAAA,YACP,iBAAiB,UAAU;AAAA,YAC3B,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,YAAI,mBAAmB;AAEvB,YAAI,iBAAiB,IAAI;AACvB,gBAAM,WAAW,MAAM,iBAAiB,KAAK;AAC7C,gBAAM,gBAAgB,CAAC,4BAAQ,gBAAM,4BAAQ,gBAAM,YAAY,SAAS,oBAAK;AAC7E,gBAAM,aAAa,SAAS,QAAQ,IAAI,OAAK,EAAE,YAAY,KAAK,KAAK,CAAC;AAEtE,qBAAW,QAAQ,eAAe;AAChC,gBAAI,WAAW,SAAS,IAAI,GAAG;AAC7B,iCAAmB;AACnB;AAAA,YACF;AAAA,UACF;AAEA,cAAI,CAAC,WAAW,SAAS,gBAAgB,KAAK,WAAW,SAAS,GAAG;AACnE,+BAAmB,WAAW,CAAC;AAAA,UACjC;AAAA,QACF;AAEA,cAAM,SAAS,iDAAiD,kBAAkB;AAClF,cAAM,WAAW,MAAM,MAAM,QAAQ;AAAA,UACnC,SAAS;AAAA,YACP,iBAAiB,UAAU;AAAA,YAC3B,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,YAAI,SAAS,IAAI;AACf,gBAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,cAAI,KAAK,UAAU,KAAK,OAAO,SAAS,GAAG;AAEzC,qBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,oBAAM,MAAM,KAAK,OAAO,CAAC;AACzB,kBAAI,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAC3B,sBAAM,UAAU;AAAA,kBACd,IAAI,QAAQ,IAAI;AAAA,kBAChB,MAAM,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK;AAAA,kBAC7B,OAAO,WAAW,IAAI,CAAC,CAAC,KAAK;AAAA,kBAC7B,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,IAAI;AAAA,gBAC5C;AAEA,oBAAI,QAAQ,QAAQ,QAAQ,QAAQ,GAAG;AACrC,2BAAS,KAAK,OAAO;AAAA,gBACvB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,IAAI,SAAS,KAAK,UAAU;AAAA,YACjC,SAAS;AAAA,YACT;AAAA,YACA,WAAW;AAAA,YACX,MAAM,iCAAQ;AAAA,UAChB,CAAC,GAAG;AAAA,YACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD,CAAC;AAAA,QACH;AAAA,MACF,SAAS,OAAP;AACA,gBAAQ,MAAM,2DAAmB,KAAK;AAAA,MACxC;AAAA,IACF;AAGA,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,UAAU;AAAA,QACR,EAAE,IAAI,SAAS,MAAM,6BAAS,OAAO,KAAK,MAAM,SAAI;AAAA,QACpD,EAAE,IAAI,SAAS,MAAM,6BAAS,OAAO,KAAK,MAAM,SAAI;AAAA,QACpD,EAAE,IAAI,SAAS,MAAM,6BAAS,OAAO,KAAK,MAAM,SAAI;AAAA,MACtD;AAAA,MACA,MAAM;AAAA,IACR,CAAC,GAAG;AAAA,MACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EAEH,SAAS,OAAP;AACA,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO,yEAAkB,MAAM;AAAA,IACjC,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AACF;AA1Me;AA4Mf,eAAe,qBAAqB,SAAS,KAAK;AAChD,MAAI;AACF,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,cAAc,IAAI,aAAa,IAAI,MAAM;AAE/C,QAAI,CAAC,aAAa;AAChB,aAAO,IAAI,SAAS,8CAAW,EAAE,QAAQ,IAAI,CAAC;AAAA,IAChD;AAEA,UAAM,OAAO,KAAK,MAAM,mBAAmB,WAAW,CAAC;AAGvD,UAAM,OAAO,oBAAoB,IAAI;AAErC,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EAEH,SAAS,OAAP;AACA,WAAO,IAAI,SAAS,+EAAmB,MAAM,SAAS,EAAE,QAAQ,IAAI,CAAC;AAAA,EACvE;AACF;AAxBe;AA2Bf,SAAS,oBAAoB,MAAM;AACjC,QAAM,gBAAgB,QAAQ,IAAI,KAAK,KAAK,WAAW,EAAE,YAAY,IACnE,OAAO,IAAI,KAAK,KAAK,WAAW,EAAE,SAAS,IAAI,CAAC,EAAE,SAAS,GAAG,GAAG,IACjE,OAAO,IAAI,KAAK,KAAK,WAAW,EAAE,QAAQ,CAAC,EAAE,SAAS,GAAG,GAAG,IAC5D,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,GAAI,CAAC,EAAE,SAAS,GAAG,GAAG;AAG1D,MAAI,eAAe;AAAA,IACjB,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAEA,MAAI,KAAK,cAAc,aAAa;AAClC,mBAAe;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,EACF,WAAW,KAAK,cAAc,YAAY;AACxC,mBAAe;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,EACF;AAEA,SAAO;AAAA;AAAA;AAAA;AAAA,sCAIc,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wDAyRkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6CAWD,KAAK;AAAA;AAAA;AAAA;AAAA,6CAIL,IAAI,KAAK,KAAK,WAAW,EAAE,mBAAmB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6CAQrD,aAAa;AAAA;AAAA;AAAA;AAAA,6CAIb,aAAa;AAAA;AAAA;AAAA;AAAA,6CAIb,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAgBxC,KAAK,MAAM,IAAI,CAAC,MAAM,UAAU;AAAA;AAAA,8CAEJ,QAAQ;AAAA,0BAC5B,KAAK;AAAA,8CACe,KAAK;AAAA,qDACE,KAAK,UAAU,eAAe,SAAS,EAAE,uBAAuB,EAAE,CAAC;AAAA,qDACnE,KAAK,OAAO,eAAe,SAAS,EAAE,uBAAuB,EAAE,CAAC;AAAA;AAAA,iBAEpG,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wCAOc,KAAK,SAAS,eAAe,SAAS,EAAE,uBAAuB,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA,wCAIlE,KAAK,YAAY,eAAe,SAAS,EAAE,uBAAuB,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA,wCAIrE,KAAK,MAAM,eAAe,SAAS,EAAE,uBAAuB,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yCAQ9D,KAAK,mBAAmB,GAAG,eAAe,SAAS,EAAE,uBAAuB,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA,wCAIhF,KAAK,MAAM,eAAe,SAAS,EAAE,uBAAuB,EAAE,CAAC;AAAA;AAAA,kEAErC,KAAK,YAAY,IAAI,YAAY,KAAK,UAAU,IAAI,YAAY;AAAA,sCAC5F,KAAK,YAAY,IAAI,YAAY,KAAK,UAAU,IAAI,YAAY;AAAA,oBAClF,KAAK,YAAY,IAAI,8BAAU,KAAK,UAAU,IAAI,mCAAU;AAAA;AAAA,qDAE3B,KAAK,YAAY,IAAI,YAAY,KAAK,UAAU,IAAI,YAAY;AAAA,qBAChG,KAAK,UAAU,IAAI,KAAK,QAAQ,eAAe,SAAS,EAAE,uBAAuB,EAAE,CAAC,KAAK,KAAK,WAAW,GAAG,eAAe,SAAS,EAAE,uBAAuB,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,cAKvK,KAAK,QAAQ;AAAA;AAAA;AAAA,qBAGN,KAAK;AAAA;AAAA,gBAEV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qHASoB,oBAAI,KAAK,GAAE,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAMrE;AA1aS;AA6aT,SAAS,qBAAqB,QAAQ,YAAY;AAGhD,QAAM,WAAW,0CAA0C;AAE3D,SAAO;AAAA;AAAA;AAAA;AAAA,iBAIQ,aAAa,aAAa,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAqIrC,aAAa,6BAA6B,sBAAsB;AAAA;AAAA;AAAA,8DAGhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBA0B3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BnB;AAvMS;;;AC/2ET,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAP;AACD,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAP;AACD,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAoE;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EARS;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,iCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAlBM;AAoBN,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,CACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,IAEA,cAA0B,CAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD;AAAA,IAEA,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["error", "lastBalance"]}