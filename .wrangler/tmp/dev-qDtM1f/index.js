var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-vFfCd4/strip-cf-connecting-ip-header.js
function stripCfConnectingIPHeader(input, init) {
  const request = new Request(input, init);
  request.headers.delete("CF-Connecting-IP");
  return request;
}
__name(stripCfConnectingIPHeader, "stripCfConnectingIPHeader");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    return Reflect.apply(target, thisArg, [
      stripCfConnectingIPHeader.apply(null, argArray)
    ]);
  }
});

// src/data/customers.json
var customers_default = {
  companies: [
    {
      id: "liang-xin",
      name: "\u91CF\u5FC3",
      clients: [
        {
          id: "lx-client1",
          name: "\u767E\u5408\u85E5\u5C40",
          fileId: "10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ"
        },
        {
          id: "lx-client2",
          name: "\u5FC3\u82AF\u85E5\u5C40",
          fileId: "1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0"
        }
      ]
    },
    {
      id: "jia-xuan",
      name: "\u5609\u8431",
      clients: [
        {
          id: "jx-client1",
          name: "\u6085\u6A59\u85E5\u5C40",
          fileId: "1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo"
        },
        {
          id: "jx-client2",
          name: "\u5E78\u904B\u8349\u85E5\u5C40",
          fileId: "1S-Qpj3U8DyY318wWC0xN0M7b31grDp0Kql6gMfDEnpo"
        },
        {
          id: "jx-client3",
          name: "\u7E41\u83EF\u85E5\u5C40",
          fileId: "1O--Clvs-JjINkGISkU6e4MXFIBxEyZG1Cq5ol8W8SaM"
        },
        {
          id: "jx-client4",
          name: "\u5BB6\u6B23\u85E5\u5C40",
          fileId: "1RxkKQUL7cDJFFR91ApNrNiAYKh6lQT2CeXA7HdD1ZcU"
        },
        {
          id: "jx-client5",
          name: "\u897F\u6E56\u85E5\u5C40",
          fileId: "1F-yTFPnllUkIWuHURgHXV-E31fMuUC83kC6q5AXfaSc"
        },
        {
          id: "jx-client6",
          name: "\u8000\u5143\u85E5\u5C40",
          fileId: "1Q16-R1YuBhHO0pGe-_HHQM7VkBLYiyOT5BA6TeXDuCA"
        },
        {
          id: "jx-client7",
          name: "\u611B\u7DAD\u5EB7\u85E5\u5C40",
          fileId: "1rAiGha2Rp5-mNVXLOBrNQZW3MlE-8PKuMxx0A7wiKtE"
        },
        {
          id: "jx-client8",
          name: "\u666F\u8CC0\u85E5\u5C40",
          fileId: "1g6Lj32CubziW3PuxKridRBJCEW0V6vppMfz60EhDHlI"
        },
        {
          id: "jx-client9",
          name: "\u8FB0\u9D3B\u85E5\u5C40",
          fileId: "1ZIbx7MtUiempMfp4NDxyoyLY3HE1LkSSfrFgQouju2I"
        },
        {
          id: "jx-client10",
          name: "\u8C50\u539F\u798F\u502B\u85E5\u5C40",
          fileId: "1TwTsnGO5MXKhX9pK7nL9xQAVCndfGt4tksznFiLrt50"
        },
        {
          id: "jx-client11",
          name: "\u5317\u6597\u798F\u502B\u85E5\u5C40",
          fileId: "166vUmqixAedk_ds9DzopISBI2pRfKBzVQu0eL4xTlxQ"
        },
        {
          id: "jx-client12",
          name: "\u682A\u4E00\u85E5\u5C40",
          fileId: "1s1Qem6dAOGp06SVxGI6_Q57YBS2WwMNBO0HjuxEzoy0"
        },
        {
          id: "jx-client13",
          name: "\u5609\u9DB4\u85E5\u5C40",
          fileId: "1MPUu5CjHIPraccilf5AvKhLBLnJdmPEOINlsgABTdI4"
        },
        {
          id: "jx-client14",
          name: "\u96EA\u4EC1\u85E5\u5C40",
          fileId: "1Hatilpfnrcm2rn-8HsTOxiY-U56waMflbCzzHRuc1ak"
        }
      ]
    }
  ]
};

// src/index.js
var src_default = {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    if (path === "/" || path === "") {
      return new Response(getHomePage(), {
        headers: { "Content-Type": "text/html" }
      });
    }
    if (path === "/oauth/callback") {
      return handleOAuthCallback(request, env);
    }
    if (path === "/auth") {
      return handleAuth(env);
    }
    if (path === "/list-files") {
      return handleListFiles(request, env);
    }
    if (path === "/extract") {
      return handleExtractExcel(request, env);
    }
    if (path === "/customer-form") {
      return handleCustomerForm(request, env);
    }
    if (path === "/create-invoice") {
      return handleCreateInvoice(request, env);
    }
    if (path === "/submit-invoice") {
      return handleSubmitInvoice(request, env);
    }
    if (path === "/preview-invoice") {
      return handlePreviewInvoice(request, env);
    }
    if (path === "/api/get-last-balance") {
      return handleGetLastBalance(request, env);
    }
    if (path === "/api/get-products") {
      return handleGetProductsFromExcel(request, env);
    }
    return new Response("Not found", { status: 404 });
  }
};
function getHomePage() {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Google Drive Excel \u63D0\u53D6\u5668</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
          }
          button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
          }
          button:hover {
            background-color: #3367d6;
          }
          .container {
            margin-top: 50px;
            text-align: center;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>\u{1F4CB} \u51FA\u8CA8\u55AE\u7CFB\u7D71</h1>
          <p>\u9019\u500B\u7CFB\u7D71\u53EF\u4EE5\u5E6B\u52A9\u60A8\u7BA1\u7406\u85E5\u5C40\u51FA\u8CA8\u55AE\uFF0C\u4E26\u5F9E Google Sheets \u4E2D\u8B80\u53D6\u771F\u5BE6\u7684\u9918\u984D\u8CC7\u6599</p>
          <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <strong>\u6388\u6B0A\u5F8C\u53EF\u4EE5\uFF1A</strong><br>
            \u2022 \u5F9E\u771F\u5BE6\u7684 Google Sheets \u8B80\u53D6\u524D\u671F\u9918\u984D<br>
            \u2022 \u81EA\u52D5\u66F4\u65B0\u5BA2\u6236\u9918\u984D\u8CC7\u6599<br>
            \u2022 \u5B8C\u6574\u7684\u51FA\u8CA8\u55AE\u7BA1\u7406\u529F\u80FD
          </div>
          <button onclick="window.location.href='/auth'" style="margin: 10px;">\u{1F510} \u6388\u6B0A Google Drive \u5B58\u53D6</button>
          <button onclick="window.location.href='/customer-form'" style="margin: 10px; background-color: #6c757d;">\u{1F680} \u76F4\u63A5\u4F7F\u7528\uFF08\u6A21\u64EC\u8CC7\u6599\uFF09</button>
        </div>
      </body>
    </html>
  `;
}
__name(getHomePage, "getHomePage");
async function handleAuth(env) {
  const clientId = env.CLIENT_ID;
  const redirectUri = env.REDIRECT_URI;
  const scopes = [
    "https://www.googleapis.com/auth/spreadsheets",
    "https://www.googleapis.com/auth/drive",
    "https://www.googleapis.com/auth/drive.readonly"
  ].join(" ");
  const authUrl = new URL("https://accounts.google.com/o/oauth2/auth");
  authUrl.searchParams.set("client_id", clientId);
  authUrl.searchParams.set("redirect_uri", redirectUri);
  authUrl.searchParams.set("response_type", "code");
  authUrl.searchParams.set("scope", scopes);
  authUrl.searchParams.set("access_type", "offline");
  authUrl.searchParams.set("prompt", "consent");
  return Response.redirect(authUrl.toString(), 302);
}
__name(handleAuth, "handleAuth");
async function handleOAuthCallback(request, env) {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const error = url.searchParams.get("error");
  if (error) {
    return new Response(`OAuth\u8A8D\u8B49\u932F\u8AA4: ${error}`, { status: 400 });
  }
  if (!code) {
    return new Response("\u672A\u6536\u5230\u6388\u6B0A\u78BC", { status: 400 });
  }
  try {
    const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      body: new URLSearchParams({
        client_id: env.CLIENT_ID,
        client_secret: env.CLIENT_SECRET,
        code,
        grant_type: "authorization_code",
        redirect_uri: env.REDIRECT_URI
      })
    });
    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      return new Response(`\u4EE4\u724C\u4EA4\u63DB\u5931\u6557: ${errorText}`, { status: 400 });
    }
    const tokens = await tokenResponse.json();
    const response = new Response(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>\u6388\u6B0A\u6210\u529F</title>
          <meta charset="UTF-8">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .success { color: #28a745; }
            .token-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <h1 class="success">\u2705 \u6388\u6B0A\u6210\u529F\uFF01</h1>
          <p>\u60A8\u5DF2\u6210\u529F\u9023\u63A5\u5230 Google Drive\u3002\u73FE\u5728\u53EF\u4EE5\u8B80\u53D6\u771F\u5BE6\u7684 Google Sheets \u8CC7\u6599\u4E86\u3002</p>
          <div class="token-info">
            <small>Token\u5DF2\u4FDD\u5B58\uFF0C\u6709\u6548\u671F\uFF1A${tokens.expires_in || 3600}\u79D2</small>
          </div>
          <button onclick="window.location.href='/customer-form'" style="background: #007bff; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;">
            \u524D\u5F80\u51FA\u8CA8\u55AE\u7CFB\u7D71
          </button>

          <script>
            // \u540C\u6642\u5B58\u5132\u5728localStorage\u4E2D\u4F5C\u70BA\u5099\u7528
            localStorage.setItem('access_token', '${tokens.access_token}');
            localStorage.setItem('token_expires', Date.now() + ${(tokens.expires_in || 3600) * 1e3});
            if ('${tokens.refresh_token}') {
              localStorage.setItem('refresh_token', '${tokens.refresh_token}');
            }
          <\/script>
        </body>
      </html>
    `, {
      headers: {
        "Content-Type": "text/html",
        "Set-Cookie": [
          `access_token=${tokens.access_token}; Path=/; Max-Age=${tokens.expires_in || 3600}; SameSite=Lax`,
          `token_type=${tokens.token_type || "Bearer"}; Path=/; Max-Age=${tokens.expires_in || 3600}; SameSite=Lax`
        ]
      }
    });
    return response;
  } catch (error2) {
    return new Response(`OAuth\u8655\u7406\u932F\u8AA4: ${error2.message}`, { status: 500 });
  }
}
__name(handleOAuthCallback, "handleOAuthCallback");
async function handleListFiles(request, env) {
  return new Response("\u5217\u51FA\u6587\u4EF6\u529F\u80FD\u5C1A\u672A\u5BE6\u73FE", { status: 501 });
}
__name(handleListFiles, "handleListFiles");
async function handleExtractExcel(request, env) {
  try {
    const url = new URL(request.url);
    const fileId = url.searchParams.get("fileId");
    const clientName = url.searchParams.get("clientName");
    if (!fileId) {
      return new Response(JSON.stringify({ error: "\u7F3A\u5C11 fileId \u53C3\u6578" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }
    const html = getEmbeddedSheetPage(fileId, clientName);
    return new Response(html, {
      status: 200,
      headers: {
        "Content-Type": "text/html; charset=utf-8"
      }
    });
  } catch (error) {
    console.error("\u986F\u793AExcel\u7DE8\u8F2F\u5668\u6642\u767C\u751F\u932F\u8AA4:", error);
    return new Response(JSON.stringify({
      error: "\u986F\u793AExcel\u7DE8\u8F2F\u5668\u6642\u767C\u751F\u932F\u8AA4",
      details: error.message
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}
__name(handleExtractExcel, "handleExtractExcel");
async function handleCustomerForm(request, env) {
  const allCustomers = [];
  customers_default.companies.forEach((company) => {
    company.clients.forEach((client) => {
      allCustomers.push({
        company: company.name,
        name: client.name,
        id: client.id,
        fileId: client.fileId || "\u8ACB\u8A2D\u5B9A\u6A94\u6848ID"
      });
    });
  });
  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>\u9078\u64C7\u5BA2\u6236 - \u51FA\u8CA8\u55AE\u7CFB\u7D71</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
          }

          .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            padding: 40px;
            max-width: 500px;
            width: 100%;
          }

          h1 {
            color: #333;
            text-align: center;
            margin-bottom: 10px;
            font-size: 28px;
          }

          .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
          }

          .form-group {
            margin-bottom: 25px;
          }

          label {
            display: block;
            color: #555;
            font-weight: 500;
            margin-bottom: 10px;
            font-size: 14px;
          }

          select, input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
          }

          select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }

          .company-section {
            margin-bottom: 25px;
          }

          .company-title {
            color: #555;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            padding-left: 5px;
            border-left: 3px solid #667eea;
          }

          .customer-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 15px;
          }

          .customer-card {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
          }

          .customer-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);
          }

          .customer-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
          }

          .customer-actions {
            display: flex;
            gap: 8px;
          }

          .btn-action {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
          }

          .btn-view {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
          }

          .btn-view:hover {
            background: #e9ecef;
            transform: translateY(-1px);
          }

          .btn-create {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }

          .btn-create:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }

          .submit-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 20px;
          }

          .submit-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
          }

          .submit-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .divider {
            text-align: center;
            color: #999;
            margin: 30px 0;
            position: relative;
          }

          .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
          }

          .divider span {
            background: white;
            padding: 0 15px;
            position: relative;
          }

          .custom-input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
          }

          .custom-input-group input {
            flex: 1;
          }

          .custom-input-group button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
          }

          .custom-input-group button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
          }

          @media (max-width: 480px) {
            .container {
              padding: 30px 20px;
            }

            h1 {
              font-size: 24px;
            }

            .customer-list {
              grid-template-columns: 1fr;
            }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>\u{1F4CB} \u51FA\u8CA8\u55AE\u7CFB\u7D71</h1>
          <p class="subtitle">\u9078\u64C7\u5BA2\u6236\u4EE5\u958B\u555F\u5C0D\u61C9\u7684\u51FA\u8CA8\u55AE</p>

          <div class="form-group">
            <label for="fileId">Google Sheets \u6A94\u6848 ID</label>
            <input
              type="text"
              id="fileId"
              name="fileId"
              value="\u6BCF\u500B\u5BA2\u6236\u6709\u7368\u7ACB\u7684\u6A94\u6848ID"
              readonly
              placeholder="\u8F38\u5165 Google Sheets \u6A94\u6848 ID"
            />
          </div>

          <div class="divider">
            <span>\u5FEB\u901F\u9078\u64C7\u5BA2\u6236</span>
          </div>

          ${customers_default.companies.map((company) => `
            <div class="company-section">
              <h3 class="company-title">${company.name}</h3>
              <div class="customer-list">
                ${company.clients.map((client) => {
    return `
                  <div class="customer-card">
                    <div class="customer-name">${client.name}</div>
                    <div class="customer-id" style="font-size: 12px; color: #666; margin: 5px 0;">
                      \u6A94\u6848ID: ${client.fileId || "\u8ACB\u8A2D\u5B9A\u6A94\u6848ID"}
                    </div>
                    <div class="customer-actions">
                      <a href="/extract?fileId=${client.fileId}&clientName=${encodeURIComponent(client.name)}"
                         class="btn-action btn-view">
                        \u{1F4CA} \u67E5\u770B\u8A18\u9304
                      </a>
                      <a href="/create-invoice?fileId=${client.fileId}&clientName=${encodeURIComponent(client.name)}"
                         class="btn-action btn-create">
                        \u{1F4DD} \u5EFA\u7ACB\u51FA\u8CA8\u55AE
                      </a>
                    </div>
                  </div>
                  `;
  }).join("")}
              </div>
            </div>
          `).join("")}

          <div class="divider">
            <span>\u6216\u8F38\u5165\u81EA\u8A02\u5BA2\u6236\u540D\u7A31</span>
          </div>

          <div class="form-group">
            <label for="customClient">\u81EA\u8A02\u5BA2\u6236\u540D\u7A31</label>
            <div class="custom-input-group">
              <input
                type="text"
                id="customClient"
                name="customClient"
                placeholder="\u8F38\u5165\u5BA2\u6236\u540D\u7A31"
              />
              <button onclick="goToCustomClient()">\u958B\u555F</button>
            </div>
          </div>
        </div>

        <script>
          // \u6BCF\u500B\u5BA2\u6236\u73FE\u5728\u90FD\u6709\u56FA\u5B9A\u7684\u6A94\u6848ID\uFF0C\u4E0D\u9700\u8981\u52D5\u614B\u66F4\u65B0\u9023\u7D50

          // \u524D\u5F80\u81EA\u8A02\u5BA2\u6236\uFF08\u4F7F\u7528\u9810\u8A2D\u6A94\u6848ID\uFF09
          function goToCustomClient() {
            const clientName = document.getElementById('customClient').value;

            if (!clientName.trim()) {
              alert('\u8ACB\u8F38\u5165\u5BA2\u6236\u540D\u7A31');
              return;
            }

            // \u4F7F\u7528\u9810\u8A2D\u6A94\u6848ID\u4F5C\u70BA\u81EA\u8A02\u5BA2\u6236
            const defaultFileId = '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ';
            window.location.href = \`/extract?fileId=\${encodeURIComponent(defaultFileId)}&clientName=\${encodeURIComponent(clientName)}\`;
          }

          // Enter \u9375\u9001\u51FA
          document.getElementById('customClient').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
              goToCustomClient();
            }
          });
        <\/script>
      </body>
    </html>
  `;
  return new Response(html, {
    status: 200,
    headers: {
      "Content-Type": "text/html; charset=utf-8"
    }
  });
}
__name(handleCustomerForm, "handleCustomerForm");
async function handleCreateInvoice(request, env) {
  const url = new URL(request.url);
  const clientName = url.searchParams.get("clientName") || "";
  const fileId = url.searchParams.get("fileId") || env.DEFAULT_FILE_ID || "10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ";
  let companyId = "";
  let companyName = "";
  let customerId = "";
  for (const company of customers_default.companies) {
    const client = company.clients.find((c) => c.name === clientName);
    if (client) {
      companyId = company.id;
      companyName = company.name;
      customerId = client.id;
      break;
    }
  }
  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>\u5275\u5EFA\u51FA\u8CA8\u8A18\u9304 - ${clientName}</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            padding: 20px;
          }

          .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
          }

          .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
          }

          .form-section {
            padding: 30px;
          }

          .form-group {
            margin-bottom: 20px;
          }

          label {
            display: block;
            color: #555;
            font-weight: 500;
            margin-bottom: 8px;
          }

          input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
          }

          input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
          }

          .row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
          }

          .items-section {
            border-top: 1px solid #e1e5e9;
            padding-top: 20px;
            margin-top: 20px;
          }

          .item-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 10px;
            align-items: end;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
          }

          .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
          }

          .btn-primary {
            background: #667eea;
            color: white;
          }

          .btn-success {
            background: #28a745;
            color: white;
          }

          .btn-danger {
            background: #dc3545;
            color: white;
          }

          .btn-secondary {
            background: #6c757d;
            color: white;
          }

          .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          }

          .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
          }

          .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }

          .total {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            border-top: 2px solid #667eea;
            padding-top: 10px;
          }

          @media (max-width: 768px) {
            .row {
              grid-template-columns: 1fr;
            }

            .item-row {
              grid-template-columns: 1fr;
            }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>\u{1F4CB} \u5275\u5EFA\u51FA\u8CA8\u8A18\u9304</h1>
            <p>\u5BA2\u6236\uFF1A${clientName} (${companyName})</p>
          </div>

          <div class="form-section">
            <form id="invoiceForm">
              <input type="hidden" name="fileId" value="${fileId}">
              <input type="hidden" name="clientName" value="${clientName}">
              <input type="hidden" name="companyId" value="${companyId}">
              <input type="hidden" name="companyName" value="${companyName}">

              <div class="row">
                <div class="form-group">
                  <label for="invoiceDate">\u51FA\u8CA8\u65E5\u671F</label>
                  <input type="date" id="invoiceDate" name="invoiceDate" required>
                </div>
                <div class="form-group">
                  <label for="shippingFee">\u90F5\u8CBB</label>
                  <input type="number" id="shippingFee" name="shippingFee" min="0" value="0" onchange="calculateTotal()">
                </div>
              </div>

              <div class="row">
                <div class="form-group">
                  <label for="previousBalance">\u524D\u671F\u9918\u984D</label>
                  <div style="display: flex; gap: 10px; align-items: center;">
                    <input type="number" id="previousBalance" name="previousBalance" step="0.01" value="0" onchange="calculateTotal()" readonly style="flex: 1;">
                    <button type="button" onclick="fetchLastBalance()" style="padding: 8px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">\u5F9EExcel\u6293\u53D6</button>
                  </div>
                </div>
                <div class="form-group">
                  <label for="paidAmount">\u672C\u6B21\u9918\u984D</label>
                  <input type="number" id="paidAmount" name="paidAmount" min="0" step="0.01" value="0" onchange="calculateTotal()">
                </div>
              </div>

              <div class="items-section">
                <h3>\u5546\u54C1\u660E\u7D30</h3>
                <div id="itemsList">
                  <div class="item-row">
                    <div>
                      <label>\u5546\u54C1\u540D\u7A31</label>
                      <select name="itemName[]" required onchange="onProductChange(this)">
                        <option value="">\u8ACB\u9078\u64C7\u5546\u54C1</option>
                      </select>
                    </div>
                    <div>
                      <label>\u6578\u91CF</label>
                      <input type="number" name="quantity[]" min="1" value="1" required onchange="calculateTotal()">
                    </div>
                    <div>
                      <label>\u55AE\u50F9</label>
                      <input type="number" name="unitPrice[]" min="0" step="0.01" required onchange="calculateTotal()" readonly>
                    </div>
                    <div>
                      <label>\u91D1\u984D</label>
                      <input type="number" name="amount[]" readonly>
                    </div>
                    <div>
                      <label>&nbsp;</label>
                      <button type="button" class="btn btn-danger" onclick="removeItem(this)">\u522A\u9664</button>
                    </div>
                  </div>
                </div>

                <button type="button" class="btn btn-secondary" onclick="addItem()">+ \u65B0\u589E\u5546\u54C1</button>
              </div>

              <div class="form-group">
                <label for="notes">\u5099\u8A3B</label>
                <textarea id="notes" name="notes" rows="3" placeholder="\u8F38\u5165\u5099\u8A3B\u8CC7\u8A0A"></textarea>
              </div>

              <div class="summary">
                <div class="summary-row">
                  <span>\u5546\u54C1\u5C0F\u8A08\uFF1A</span>
                  <span id="subtotal">$0</span>
                </div>
                <div class="summary-row">
                  <span>\u90F5\u8CBB\uFF1A</span>
                  <span id="shippingDisplay">$0</span>
                </div>
                <div class="summary-row total">
                  <span>\u672C\u6B21\u61C9\u4ED8\u7E3D\u8A08\uFF1A</span>
                  <span id="total">$0</span>
                </div>
                <div style="margin: 20px 0; border-top: 1px dashed #dee2e6;"></div>
                <div class="summary-row">
                  <span>\u524D\u671F\u9918\u984D\uFF1A</span>
                  <span id="previousBalanceDisplay">$0</span>
                </div>
                <div class="summary-row">
                  <span>\u6E1B\uFF1A\u672C\u6B21\u61C9\u4ED8\uFF1A</span>
                  <span id="currentTotalDisplay">$0</span>
                </div>
                <div class="summary-row">
                  <span>\u672C\u6B21\u9918\u984D\uFF1A</span>
                  <span id="paidAmountDisplay">$0</span>
                </div>
              </div>

              <div style="margin-top: 30px; text-align: center;">
                <button type="submit" class="btn btn-success" style="margin-right: 10px;">\u{1F4BE} \u5132\u5B58\u8A18\u9304</button>
                <button type="button" class="btn btn-primary" onclick="previewInvoice()">\u{1F441}\uFE0F \u9810\u89BD\u51FA\u8CA8\u55AE</button>
              </div>
            </form>
          </div>
        </div>

        <script>
          // \u5546\u54C1\u8CC7\u6599
          let productsData = [];

          // \u8A2D\u5B9A\u4ECA\u5929\u7684\u65E5\u671F\u70BA\u9810\u8A2D\u503C
          document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

          // \u8F09\u5165\u5546\u54C1\u8CC7\u6599
          async function loadProducts() {
            try {
              const fileId = document.querySelector('input[name="fileId"]').value;

              // \u6E96\u5099headers\uFF0C\u5305\u62EC\u53EF\u80FD\u7684access token
              const headers = {
                'Content-Type': 'application/json'
              };

              // \u5617\u8A66\u5F9ElocalStorage\u7372\u53D6access token
              const accessToken = localStorage.getItem('access_token');
              if (accessToken) {
                headers['Authorization'] = 'Bearer ' + accessToken;
              }

              const response = await fetch('/api/get-products', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                  fileId: fileId
                })
              });

              const result = await response.json();
              if (result.success) {
                productsData = result.products;
                populateProductOptions();

                // \u986F\u793A\u8B80\u53D6\u4F86\u6E90\u8CC7\u8A0A
                if (result.note) {
                  console.log('\u5546\u54C1\u6E05\u55AE\u4F86\u6E90:', result.note);
                }
              } else {
                console.error('\u8F09\u5165\u5546\u54C1\u8CC7\u6599\u5931\u6557:', result.error);
                // \u4F7F\u7528\u9810\u8A2D\u7684\u6A21\u64EC\u8CC7\u6599
                productsData = [
                  { id: 'demo1', name: '\u7BC4\u4F8B\u5546\u54C1A', price: 100, unit: '\u76D2' },
                  { id: 'demo2', name: '\u7BC4\u4F8B\u5546\u54C1B', price: 200, unit: '\u74F6' }
                ];
                populateProductOptions();
              }
            } catch (error) {
              console.error('\u8F09\u5165\u5546\u54C1\u8CC7\u6599\u5931\u6557:', error);
              // \u4F7F\u7528\u9810\u8A2D\u7684\u6A21\u64EC\u8CC7\u6599
              productsData = [
                { id: 'demo1', name: '\u7BC4\u4F8B\u5546\u54C1A', price: 100, unit: '\u76D2' },
                { id: 'demo2', name: '\u7BC4\u4F8B\u5546\u54C1B', price: 200, unit: '\u74F6' }
              ];
              populateProductOptions();
            }
          }

          // \u586B\u5145\u5546\u54C1\u9078\u9805\u5230\u6240\u6709\u4E0B\u62C9\u9078\u55AE
          function populateProductOptions() {
            const selects = document.querySelectorAll('select[name="itemName[]"]');
            selects.forEach(select => {
              // \u4FDD\u7559\u76EE\u524D\u9078\u64C7\u7684\u503C
              const currentValue = select.value;

              // \u6E05\u7A7A\u9078\u9805\u4E26\u91CD\u65B0\u586B\u5145
              select.innerHTML = '<option value="">\u8ACB\u9078\u64C7\u5546\u54C1</option>';

              productsData.forEach(product => {
                const option = document.createElement('option');
                option.value = product.name;
                option.textContent = \`\${product.name} - $\${product.price} (\${product.unit})\`;
                option.dataset.price = product.price;
                option.dataset.productId = product.id;
                select.appendChild(option);
              });

              // \u6062\u5FA9\u4E4B\u524D\u7684\u9078\u64C7
              if (currentValue) {
                select.value = currentValue;
              }
            });
          }

          // \u5546\u54C1\u9078\u64C7\u8B8A\u66F4\u6642\u7684\u8655\u7406
          function onProductChange(selectElement) {
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            const priceInput = selectElement.closest('.item-row').querySelector('input[name="unitPrice[]"]');

            if (selectedOption.dataset.price) {
              priceInput.value = selectedOption.dataset.price;
            } else {
              priceInput.value = '';
            }

            calculateTotal();
          }

          function addItem() {
            const itemsList = document.getElementById('itemsList');
            const newItem = document.createElement('div');
            newItem.className = 'item-row';
            newItem.innerHTML = \`
              <div>
                <label>\u5546\u54C1\u540D\u7A31</label>
                <select name="itemName[]" required onchange="onProductChange(this)">
                  <option value="">\u8ACB\u9078\u64C7\u5546\u54C1</option>
                </select>
              </div>
              <div>
                <label>\u6578\u91CF</label>
                <input type="number" name="quantity[]" min="1" value="1" required onchange="calculateTotal()">
              </div>
              <div>
                <label>\u55AE\u50F9</label>
                <input type="number" name="unitPrice[]" min="0" step="0.01" required onchange="calculateTotal()" readonly>
              </div>
              <div>
                <label>\u91D1\u984D</label>
                <input type="number" name="amount[]" readonly>
              </div>
              <div>
                <label>&nbsp;</label>
                <button type="button" class="btn btn-danger" onclick="removeItem(this)">\u522A\u9664</button>
              </div>
            \`;
            itemsList.appendChild(newItem);

            // \u70BA\u65B0\u589E\u7684\u4E0B\u62C9\u9078\u55AE\u586B\u5145\u5546\u54C1\u9078\u9805
            populateProductOptions();
          }

          function removeItem(button) {
            if (document.querySelectorAll('.item-row').length > 1) {
              button.closest('.item-row').remove();
              calculateTotal();
            }
          }

          async function fetchLastBalance() {
            const customerId = '${customerId}';
            const fileId = getFileIdForCustomer(customerId);

            if (!fileId) {
              alert('\u627E\u4E0D\u5230\u5C0D\u61C9\u7684Excel\u6A94\u6848ID');
              return;
            }

            try {
              // \u6E96\u5099headers\uFF0C\u5305\u62EC\u53EF\u80FD\u7684access token
              const headers = {
                'Content-Type': 'application/json'
              };

              // \u5617\u8A66\u5F9ElocalStorage\u7372\u53D6access token
              const accessToken = localStorage.getItem('access_token');
              if (accessToken) {
                headers['Authorization'] = 'Bearer ' + accessToken;
              }

              // \u8ABF\u7528\u5F8C\u7AEFAPI\u4F86\u6293\u53D6\u6700\u5F8C\u4E00\u7B46\u9918\u984D
              const response = await fetch('/api/get-last-balance', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                  fileId: fileId,
                  customerId: customerId
                })
              });

              if (response.ok) {
                const result = await response.json();
                document.getElementById('previousBalance').value = result.lastBalance || 0;

                // \u8A2D\u5B9A\u8868\u55AE\u4E2D\u7684\u96B1\u85CF fileId \u6B04\u4F4D
                const fileIdInput = document.querySelector('input[name="fileId"]');
                if (fileIdInput) {
                  fileIdInput.value = fileId;
                }

                calculateTotal();
              } else {
                alert('\u6293\u53D6\u9918\u984D\u5931\u6557: ' + response.statusText);
              }
            } catch (error) {
              alert('\u6293\u53D6\u9918\u984D\u6642\u767C\u751F\u932F\u8AA4: ' + error.message);
            }
          }

          function getFileIdForCustomer(customerId) {
            // \u5F9E\u5BA2\u6236\u8CC7\u6599\u4E2D\u8B80\u53D6\u5C0D\u61C9\u7684\u6A94\u6848ID
            const customersData = ${JSON.stringify(customers_default)};
            for (const company of customersData.companies) {
              const client = company.clients.find(c => c.id === customerId);
              if (client && client.fileId) {
                return client.fileId;
              }
            }
            return '10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ'; // \u9810\u8A2D\u6A94\u6848ID
          }

          function calculateTotal() {
            let subtotal = 0;
            const itemRows = document.querySelectorAll('.item-row');

            itemRows.forEach(row => {
              const quantity = parseFloat(row.querySelector('input[name="quantity[]"]').value) || 0;
              const unitPrice = parseFloat(row.querySelector('input[name="unitPrice[]"]').value) || 0;
              const amount = quantity * unitPrice;

              row.querySelector('input[name="amount[]"]').value = amount.toFixed(2);
              subtotal += amount;
            });

            const shippingFee = parseFloat(document.getElementById('shippingFee').value) || 0;
            const total = subtotal + shippingFee;
            const previousBalance = parseFloat(document.getElementById('previousBalance').value) || 0;
            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;

            // \u9918\u984D\u8A08\u7B97\uFF1A\u524D\u671F\u9918\u984D - \u672C\u6B21\u61C9\u4ED8\u7E3D\u8A08 = \u672C\u6B21\u9918\u984D
            const balance = previousBalance - total;

            // \u66F4\u65B0\u986F\u793A
            document.getElementById('subtotal').textContent = '$' + subtotal.toFixed(2);
            document.getElementById('shippingDisplay').textContent = '$' + shippingFee.toFixed(2);
            document.getElementById('total').textContent = '$' + total.toFixed(2);
            document.getElementById('previousBalanceDisplay').textContent = '$' + previousBalance.toFixed(2);
            document.getElementById('currentTotalDisplay').textContent = '$' + total.toFixed(2);
            document.getElementById('paidAmountDisplay').textContent = '$' + balance.toFixed(2);

            // \u540C\u6B65\u672C\u6B21\u9918\u984D\u5230\u6700\u4E0A\u9762\u7684\u8F38\u5165\u6B04\u4F4D\uFF08\u66AB\u6642\u79FB\u9664\u4E8B\u4EF6\u76E3\u807D\u5668\u907F\u514D\u5FAA\u74B0\uFF09
            const paidAmountElement = document.getElementById('paidAmount');
            paidAmountElement.onchange = null;
            paidAmountElement.value = balance.toFixed(2);
            paidAmountElement.onchange = calculateTotal;

          }

          document.getElementById('invoiceForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // \u6536\u96C6\u8868\u55AE\u8CC7\u6599
            const formData = new FormData(this);
            const data = {};

            // \u57FA\u672C\u8CC7\u6599
            data.fileId = formData.get('fileId');
            data.clientName = formData.get('clientName');
            data.companyId = formData.get('companyId');
            data.companyName = formData.get('companyName');
            data.invoiceDate = formData.get('invoiceDate');
            data.shippingFee = parseFloat(formData.get('shippingFee')) || 0;
            data.previousBalance = parseFloat(formData.get('previousBalance')) || 0;
            data.paidAmount = parseFloat(formData.get('paidAmount')) || 0;
            data.notes = formData.get('notes');

            // \u5546\u54C1\u8CC7\u6599
            data.items = [];
            const itemNames = formData.getAll('itemName[]');
            const quantities = formData.getAll('quantity[]');
            const unitPrices = formData.getAll('unitPrice[]');

            for (let i = 0; i < itemNames.length; i++) {
              if (itemNames[i].trim()) {
                data.items.push({
                  name: itemNames[i],
                  quantity: parseFloat(quantities[i]),
                  unitPrice: parseFloat(unitPrices[i]),
                  amount: parseFloat(quantities[i]) * parseFloat(unitPrices[i])
                });
              }
            }

            // \u8A08\u7B97\u7E3D\u8A08\u548C\u9918\u984D
            data.subtotal = data.items.reduce((sum, item) => sum + item.amount, 0);
            data.total = data.subtotal + data.shippingFee;
            data.balance = (data.previousBalance || 0) - data.total;

            // \u63D0\u4EA4\u8CC7\u6599
            fetch('/submit-invoice', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
              if (result.success) {
                alert('\u51FA\u8CA8\u8A18\u9304\u5DF2\u6210\u529F\u5132\u5B58\uFF01');
                // \u53EF\u4EE5\u91CD\u65B0\u5C0E\u5411\u6216\u6E05\u7A7A\u8868\u55AE
                window.location.href = '/customer-form';
              } else {
                alert('\u5132\u5B58\u5931\u6557\uFF1A' + result.error);
              }
            })
            .catch(error => {
              alert('\u63D0\u4EA4\u6642\u767C\u751F\u932F\u8AA4\uFF1A' + error.message);
            });
          });

          function previewInvoice() {
            // \u6536\u96C6\u8868\u55AE\u8CC7\u6599\u4E26\u958B\u555F\u9810\u89BD
            const form = document.getElementById('invoiceForm');
            const formData = new FormData(form);
            const data = {};

            // \u57FA\u672C\u8CC7\u6599
            data.fileId = formData.get('fileId');
            data.clientName = formData.get('clientName');
            data.companyId = formData.get('companyId');
            data.companyName = formData.get('companyName');
            data.invoiceDate = formData.get('invoiceDate');
            data.shippingFee = parseFloat(formData.get('shippingFee')) || 0;
            data.previousBalance = parseFloat(formData.get('previousBalance')) || 0;
            data.paidAmount = parseFloat(formData.get('paidAmount')) || 0;
            data.notes = formData.get('notes');

            // \u5546\u54C1\u8CC7\u6599
            data.items = [];
            const itemNames = formData.getAll('itemName[]');
            const quantities = formData.getAll('quantity[]');
            const unitPrices = formData.getAll('unitPrice[]');

            for (let i = 0; i < itemNames.length; i++) {
              if (itemNames[i].trim()) {
                data.items.push({
                  name: itemNames[i],
                  quantity: parseFloat(quantities[i]),
                  unitPrice: parseFloat(unitPrices[i]),
                  amount: parseFloat(quantities[i]) * parseFloat(unitPrices[i])
                });
              }
            }

            // \u8A08\u7B97\u7E3D\u8A08\u548C\u9918\u984D
            data.subtotal = data.items.reduce((sum, item) => sum + item.amount, 0);
            data.total = data.subtotal + data.shippingFee;
            data.balance = (data.previousBalance || 0) - data.total;

            // \u5C07\u8CC7\u6599\u7DE8\u78BC\u4E26\u50B3\u9001\u5230\u9810\u89BD\u9801\u9762
            const encodedData = encodeURIComponent(JSON.stringify(data));
            const previewUrl = \`/preview-invoice?data=\${encodedData}\`;

            // \u5728\u65B0\u8996\u7A97\u958B\u555F\u9810\u89BD
            window.open(previewUrl, '_blank', 'width=800,height=1000,scrollbars=yes');
          }

          // \u9801\u9762\u8F09\u5165\u6642\u521D\u59CB\u5316
          document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            calculateTotal();
          });

          // \u5982\u679CDOMContentLoaded\u5DF2\u7D93\u89F8\u767C\uFF0C\u76F4\u63A5\u57F7\u884C\u521D\u59CB\u5316
          if (document.readyState === 'loading') {
            // \u6587\u6A94\u9084\u5728\u8F09\u5165\u4E2D\uFF0C\u7B49\u5F85DOMContentLoaded\u4E8B\u4EF6
          } else {
            // \u6587\u6A94\u5DF2\u7D93\u8F09\u5165\u5B8C\u6210\uFF0C\u76F4\u63A5\u57F7\u884C\u521D\u59CB\u5316
            loadProducts();
            calculateTotal();
          }
        <\/script>
      </body>
    </html>
  `;
  return new Response(html, {
    status: 200,
    headers: {
      "Content-Type": "text/html; charset=utf-8"
    }
  });
}
__name(handleCreateInvoice, "handleCreateInvoice");
async function handleSubmitInvoice(request, env) {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }
  try {
    const data = await request.json();
    const fileId = data.fileId;
    if (!fileId) {
      return new Response(JSON.stringify({
        success: false,
        error: "\u7F3A\u5C11\u6A94\u6848ID"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }
    const accessToken = getAccessToken(request);
    try {
      const newRow = [
        data.invoiceDate,
        // A欄：日期
        data.items.map((item) => `${item.name} x${item.quantity}`).join(", "),
        // B欄：品名
        data.items.reduce((sum, item) => sum + item.quantity, 0),
        // C欄：總數量
        data.total,
        // D欄：價錢
        data.total,
        // E欄：小計
        data.balance
        // F欄：剩餘金額
      ];
      let writeSuccess = false;
      if (env.GOOGLE_API_KEY && !writeSuccess) {
        try {
          const appendUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F:append?valueInputOption=USER_ENTERED&key=${env.GOOGLE_API_KEY}`;
          const response = await fetch(appendUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              range: "A:F",
              majorDimension: "ROWS",
              values: [newRow]
            })
          });
          if (response.ok) {
            writeSuccess = true;
            console.log("\u4F7F\u7528 API Key \u6210\u529F\u5BEB\u5165");
          } else {
            console.error("API Key \u5BEB\u5165\u5931\u6557:", await response.text());
          }
        } catch (error) {
          console.error("API Key \u5BEB\u5165\u932F\u8AA4:", error);
        }
      }
      if (accessToken && !writeSuccess) {
        try {
          const appendUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F:append?valueInputOption=USER_ENTERED`;
          const response = await fetch(appendUrl, {
            method: "POST",
            headers: {
              "Authorization": `Bearer ${accessToken}`,
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              range: "A:F",
              majorDimension: "ROWS",
              values: [newRow]
            })
          });
          if (response.ok) {
            writeSuccess = true;
            console.log("\u4F7F\u7528 OAuth \u6210\u529F\u5BEB\u5165");
          } else {
            console.error("OAuth \u5BEB\u5165\u5931\u6557:", await response.text());
          }
        } catch (error) {
          console.error("OAuth \u5BEB\u5165\u932F\u8AA4:", error);
        }
      }
      if (writeSuccess) {
        return new Response(JSON.stringify({
          success: true,
          message: "\u51FA\u8CA8\u8A18\u9304\u5DF2\u6210\u529F\u5132\u5B58\u5230 Google Sheets",
          data
        }), {
          status: 200,
          headers: { "Content-Type": "application/json" }
        });
      } else {
        return new Response(JSON.stringify({
          success: false,
          error: "\u7121\u6CD5\u5BEB\u5165 Google Sheets\uFF0C\u8ACB\u6AA2\u67E5\u6B0A\u9650\u8A2D\u5B9A\u6216\u9032\u884C OAuth \u8A8D\u8B49"
        }), {
          status: 500,
          headers: { "Content-Type": "application/json" }
        });
      }
    } catch (writeError) {
      console.error("\u5BEB\u5165 Google Sheets \u5931\u6557:", writeError);
      return new Response(JSON.stringify({
        success: false,
        error: "\u5BEB\u5165\u5931\u6557: " + writeError.message
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
}
__name(handleSubmitInvoice, "handleSubmitInvoice");
function getAccessToken(request) {
  const cookieHeader = request.headers.get("Cookie");
  if (cookieHeader) {
    const cookies = cookieHeader.split(";").reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split("=");
      acc[key] = value;
      return acc;
    }, {});
    if (cookies.access_token) {
      return cookies.access_token;
    }
  }
  const authHeader = request.headers.get("Authorization");
  if (authHeader && authHeader.startsWith("Bearer ")) {
    return authHeader.slice(7);
  }
  return null;
}
__name(getAccessToken, "getAccessToken");
async function handleGetLastBalance(request, env) {
  try {
    const requestBody = await request.json();
    const { fileId, customerId } = requestBody;
    if (!fileId) {
      return new Response(JSON.stringify({ error: "\u672A\u63D0\u4F9B\u6A94\u6848ID" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }
    const accessToken = getAccessToken(request);
    if (env.GOOGLE_API_KEY) {
      try {
        const apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F?key=${env.GOOGLE_API_KEY}`;
        console.log("\u6C92\u6709 OAuth token\uFF0C\u76F4\u63A5\u4F7F\u7528 API Key:", apiUrl);
        const response = await fetch(apiUrl, {
          headers: {
            "Accept": "application/json"
          }
        });
        if (response.ok) {
          const data = await response.json();
          let lastBalance = 0;
          if (data.values && data.values.length > 0) {
            for (let i = data.values.length - 1; i >= 0; i--) {
              const row = data.values[i];
              if (row && row[5] && !isNaN(parseFloat(row[5]))) {
                lastBalance = parseFloat(row[5]);
                console.log("\u5F9EAPI Key\u627E\u5230\u9918\u984D\uFF08F\u6B04\uFF09:", lastBalance);
                break;
              }
            }
          }
          return new Response(JSON.stringify({
            lastBalance,
            note: "\u4F7F\u7528API Key\u5F9EGoogle Sheets\u8B80\u53D6\u7684\u8CC7\u6599"
          }), {
            headers: { "Content-Type": "application/json" }
          });
        } else {
          const errorText = await response.text();
          console.error("API Key \u8ACB\u6C42\u5931\u6557:", response.status, errorText);
        }
      } catch (error) {
        console.error("API Key \u5B58\u53D6\u5931\u6557:", error);
      }
    }
    if (!accessToken) {
      const mockBalancesByFileId = {
        "10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ": 53455,
        // 百合藥局檔案
        "1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0": 24800,
        // 心芯藥局檔案
        "1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo": 15600,
        // 悅橙藥局檔案
        "1S-Qpj3U8DyY318wWC0xN0M7b31grDp0Kql6gMfDEnpo": 82300,
        // 幸運草藥局檔案
        "1O--Clvs-JjINkGISkU6e4MXFIBxEyZG1Cq5ol8W8SaM": 36700,
        // 繁華藥局檔案
        "1RxkKQUL7cDJFFR91ApNrNiAYKh6lQT2CeXA7HdD1ZcU": 19200,
        // 家欣藥局檔案
        "1F-yTFPnllUkIWuHURgHXV-E31fMuUC83kC6q5AXfaSc": 64500,
        // 西湖藥局檔案
        "1Q16-R1YuBhHO0pGe-_HHQM7VkBLYiyOT5BA6TeXDuCA": 41800,
        // 耀元藥局檔案
        "1rAiGha2Rp5-mNVXLOBrNQZW3MlE-8PKuMxx0A7wiKtE": 28900,
        // 愛維康藥局檔案
        "1g6Lj32CubziW3PuxKridRBJCEW0V6vppMfz60EhDHlI": 56100,
        // 景賀藥局檔案
        "1ZIbx7MtUiempMfp4NDxyoyLY3HE1LkSSfrFgQouju2I": 33400,
        // 辰鴻藥局檔案
        "1TwTsnGO5MXKhX9pK7nL9xQAVCndfGt4tksznFiLrt50": 48700,
        // 豐原福倫藥局檔案
        "166vUmqixAedk_ds9DzopISBI2pRfKBzVQu0eL4xTlxQ": 21600,
        // 北斗福倫藥局檔案
        "1s1Qem6dAOGp06SVxGI6_Q57YBS2WwMNBO0HjuxEzoy0": 37300,
        // 株一藥局檔案
        "1MPUu5CjHIPraccilf5AvKhLBLnJdmPEOINlsgABTdI4": 59800,
        // 嘉鶴藥局檔案
        "1Hatilpfnrcm2rn-8HsTOxiY-U56waMflbCzzHRuc1ak": 26500
        // 雪仁藥局檔案
      };
      const lastBalance = mockBalancesByFileId[fileId] || 0;
      return new Response(JSON.stringify({
        lastBalance,
        note: "\u9019\u662F\u6A21\u64EC\u8CC7\u6599\u3002\u8ACB\u5148\u9032\u884COAuth\u8A8D\u8B49\u4EE5\u8B80\u53D6\u771F\u5BE6\u7684Google Sheets\u8CC7\u6599\u3002"
      }), {
        headers: { "Content-Type": "application/json" }
      });
    }
    if (accessToken) {
      try {
        const metadataUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}`;
        console.log("\u6B63\u5728\u6AA2\u67E5\u6A94\u6848\u6B0A\u9650:", metadataUrl);
        console.log("\u4F7F\u7528Access Token:", accessToken.substring(0, 20) + "...");
        const metadataResponse = await fetch(metadataUrl, {
          headers: {
            "Authorization": `Bearer ${accessToken}`,
            "Accept": "application/json"
          }
        });
        if (!metadataResponse.ok) {
          const errorText = await metadataResponse.text();
          console.error("\u6A94\u6848metadata\u53D6\u5F97\u5931\u6557:", errorText);
          const fallbackUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:E`;
          console.log("\u5617\u8A66fallback API:", fallbackUrl);
        } else {
          const metadata = await metadataResponse.json();
          console.log("\u6A94\u6848\u8CC7\u8A0A:", {
            title: metadata.properties?.title,
            sheets: metadata.sheets?.map((s) => s.properties?.title)
          });
        }
        let apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/A:F`;
        if (metadataResponse.ok) {
          const metadata = await metadataResponse.json();
          if (metadata.sheets && metadata.sheets.length > 0) {
            const firstSheetName = metadata.sheets[0].properties.title;
            apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/'${firstSheetName}'!A:F`;
            console.log("\u4F7F\u7528\u5DE5\u4F5C\u8868\u540D\u7A31:", firstSheetName);
          }
        }
        console.log("\u6B63\u5728\u547C\u53EBGoogle Sheets API:", apiUrl);
        let response = await fetch(apiUrl, {
          headers: {
            "Authorization": `Bearer ${accessToken}`,
            "Accept": "application/json"
          }
        });
        if (!response.ok) {
          console.log("OAuth\u547C\u53EB\u5931\u6557\uFF0C\u5617\u8A66API Key\u65B9\u5F0F");
          const apiKeyUrl = `${apiUrl}?key=${env.GOOGLE_API_KEY}`;
          console.log("\u4F7F\u7528API Key URL:", apiKeyUrl);
          response = await fetch(apiKeyUrl, {
            headers: {
              "Accept": "application/json"
            }
          });
          if (!response.ok) {
            console.log("API Key\u4E5F\u5931\u6557\uFF0C\u5617\u8A66CSV\u532F\u51FA\u65B9\u5F0F");
            const csvUrl = `https://docs.google.com/spreadsheets/d/${fileId}/export?format=csv`;
            console.log("\u5617\u8A66CSV URL:", csvUrl);
            const csvResponse = await fetch(csvUrl);
            if (csvResponse.ok) {
              const csvText = await csvResponse.text();
              console.log("CSV\u8CC7\u6599\u9577\u5EA6:", csvText.length);
              const lines = csvText.split("\n").filter((line) => line.trim());
              for (let i = lines.length - 1; i >= 0; i--) {
                const cols = lines[i].split(",");
                if (cols[5] && !isNaN(parseFloat(cols[5]))) {
                  const balance = parseFloat(cols[5]);
                  console.log("\u5F9ECSV\u627E\u5230\u9918\u984D\uFF08F\u6B04\uFF09:", balance);
                  return new Response(JSON.stringify({
                    lastBalance: balance,
                    note: "\u5F9ECSV\u532F\u51FA\u8B80\u53D6\u7684\u8CC7\u6599\uFF08F\u6B04\u9918\u984D\uFF09"
                  }), {
                    headers: { "Content-Type": "application/json" }
                  });
                }
              }
            }
            response = csvResponse;
          }
        }
        if (!response.ok) {
          const errorText = await response.text();
          console.error("Google Sheets API\u932F\u8AA4:", errorText);
          console.error("Response status:", response.status, response.statusText);
          let errorNote = "";
          if (response.status === 401) {
            errorNote = "\u8A8D\u8B49\u5931\u6548\u6216\u672A\u6388\u6B0A\u3002\u8ACB\u91CD\u65B0\u9032\u884COAuth\u8A8D\u8B49\uFF0C\u76EE\u524D\u4F7F\u7528\u6A21\u64EC\u8CC7\u6599\u3002";
          } else if (response.status === 403) {
            errorNote = "\u7121\u6B0A\u9650\u5B58\u53D6\u6B64Google Sheets\u6A94\u6848\u3002\u8ACB\u78BA\u8A8D\uFF1A1) \u6A94\u6848\u5DF2\u5206\u4EAB\u7D66\u6388\u6B0A\u5E33\u6236\uFF0C\u6216 2) \u6A94\u6848\u8A2D\u70BA\u300C\u77E5\u9053\u9023\u7D50\u7684\u4F7F\u7528\u8005\u53EF\u6AA2\u8996\u300D";
          } else if (response.status === 404) {
            errorNote = "\u627E\u4E0D\u5230\u6307\u5B9A\u7684Google Sheets\u6A94\u6848\u3002\u8ACB\u6AA2\u67E5\u6A94\u6848ID\u662F\u5426\u6B63\u78BA";
          } else {
            errorNote = `API\u547C\u53EB\u5931\u6557\uFF1A${response.status} ${response.statusText}`;
          }
          const mockBalancesByFileId = {
            "10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ": 53455,
            "1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0": 24800,
            "1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo": 15600
          };
          const lastBalance2 = mockBalancesByFileId[fileId] || 0;
          return new Response(JSON.stringify({
            lastBalance: lastBalance2,
            note: errorNote,
            fallback: true
          }), {
            headers: { "Content-Type": "application/json" }
          });
        }
        const data = await response.json();
        let lastBalance = 0;
        if (data.values && data.values.length > 0) {
          for (let i = data.values.length - 1; i >= 0; i--) {
            const row = data.values[i];
            if (row && row[5] && !isNaN(parseFloat(row[5]))) {
              lastBalance = parseFloat(row[5]);
              console.log("\u5F9EAPI\u627E\u5230\u9918\u984D\uFF08F\u6B04\uFF09:", lastBalance);
              break;
            }
          }
        }
        return new Response(JSON.stringify({
          lastBalance,
          note: "\u5F9EGoogle Sheets\u8B80\u53D6\u7684\u771F\u5BE6\u8CC7\u6599"
        }), {
          headers: { "Content-Type": "application/json" }
        });
      } catch (apiError) {
        console.error("Google Sheets API\u547C\u53EB\u932F\u8AA4:", apiError);
        const mockBalancesByFileId = {
          "10U0wKKkVIKC4hOxJ8k-SOyzJUTKDh7Q9-hii9cCXhNQ": 53455,
          "1SWPhTTdF3PrFRnQUXEBegr9B5LDfOBP_R0f9K9cRmy0": 24800,
          "1JvE_DsTOWDxwJG4ckaKsMvgyzBUMFeD_ZDzgpxtCALo": 15600
        };
        const lastBalance = mockBalancesByFileId[fileId] || 0;
        return new Response(JSON.stringify({
          lastBalance,
          note: `API\u547C\u53EB\u7570\u5E38\uFF0C\u4F7F\u7528\u6A21\u64EC\u8CC7\u6599\u3002\u932F\u8AA4: ${apiError.message}`
        }), {
          headers: { "Content-Type": "application/json" }
        });
      }
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: "\u8655\u7406\u8ACB\u6C42\u6642\u767C\u751F\u932F\u8AA4: " + error.message }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}
__name(handleGetLastBalance, "handleGetLastBalance");
async function handleGetProductsFromExcel(request, env) {
  try {
    const requestBody = await request.json();
    const { fileId } = requestBody;
    if (!fileId) {
      return new Response(JSON.stringify({
        success: false,
        error: "\u672A\u63D0\u4F9B\u6A94\u6848ID"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }
    const accessToken = getAccessToken(request);
    let products = [];
    if (env.GOOGLE_API_KEY) {
      try {
        const metadataUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}?key=${env.GOOGLE_API_KEY}`;
        const metadataResponse = await fetch(metadataUrl, {
          headers: {
            "Accept": "application/json"
          }
        });
        let productSheetName = "\u5546\u54C1\u6E05\u55AE";
        if (metadataResponse.ok) {
          const metadata = await metadataResponse.json();
          console.log("\u6A94\u6848\u5DE5\u4F5C\u8868:", metadata.sheets?.map((s) => s.properties?.title));
          const possibleNames = ["\u5546\u54C1\u6E05\u55AE", "\u5546\u54C1", "\u7522\u54C1\u6E05\u55AE", "\u7522\u54C1", "Products", "Items", "\u5DE5\u4F5C\u54C1"];
          const sheetNames = metadata.sheets?.map((s) => s.properties?.title) || [];
          for (const name of possibleNames) {
            if (sheetNames.includes(name)) {
              productSheetName = name;
              break;
            }
          }
          if (!sheetNames.includes(productSheetName) && sheetNames.length > 1) {
            productSheetName = sheetNames[1];
          }
        }
        const apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/'${productSheetName}'!A:C?key=${env.GOOGLE_API_KEY}`;
        console.log("\u8B80\u53D6\u5546\u54C1\u6E05\u55AE:", apiUrl);
        const response = await fetch(apiUrl, {
          headers: {
            "Accept": "application/json"
          }
        });
        if (response.ok) {
          const data = await response.json();
          console.log("\u5546\u54C1\u6E05\u55AE\u539F\u59CB\u8CC7\u6599:", data);
          if (data.values && data.values.length > 0) {
            for (let i = 0; i < data.values.length; i++) {
              const row = data.values[i];
              if (row && row[0] && row[1]) {
                const product = {
                  id: `prod_${i + 1}`,
                  name: row[0].toString().trim(),
                  price: parseFloat(row[1]) || 0,
                  unit: row[2] ? row[2].toString().trim() : "\u500B"
                };
                if (product.name && product.price > 0) {
                  products.push(product);
                }
              }
            }
          }
          console.log("\u89E3\u6790\u5F8C\u7684\u5546\u54C1\u6E05\u55AE:", products);
          return new Response(JSON.stringify({
            success: true,
            products,
            sheetName: productSheetName,
            note: `\u5F9E\u5DE5\u4F5C\u8868\u300C${productSheetName}\u300D\u8B80\u53D6\u7684\u5546\u54C1\u6E05\u55AE`
          }), {
            headers: { "Content-Type": "application/json" }
          });
        } else {
          console.error("\u8B80\u53D6\u5546\u54C1\u6E05\u55AE\u5931\u6557:", await response.text());
        }
      } catch (error) {
        console.error("API Key \u8B80\u53D6\u5546\u54C1\u6E05\u55AE\u5931\u6557:", error);
      }
    }
    if (accessToken && products.length === 0) {
      try {
        const metadataUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}`;
        const metadataResponse = await fetch(metadataUrl, {
          headers: {
            "Authorization": `Bearer ${accessToken}`,
            "Accept": "application/json"
          }
        });
        let productSheetName = "\u5546\u54C1\u6E05\u55AE";
        if (metadataResponse.ok) {
          const metadata = await metadataResponse.json();
          const possibleNames = ["\u5546\u54C1\u6E05\u55AE", "\u5546\u54C1", "\u7522\u54C1\u6E05\u55AE", "\u7522\u54C1", "Products", "Items", "\u5DE5\u4F5C\u54C1"];
          const sheetNames = metadata.sheets?.map((s) => s.properties?.title) || [];
          for (const name of possibleNames) {
            if (sheetNames.includes(name)) {
              productSheetName = name;
              break;
            }
          }
          if (!sheetNames.includes(productSheetName) && sheetNames.length > 1) {
            productSheetName = sheetNames[1];
          }
        }
        const apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${fileId}/values/'${productSheetName}'!A:C`;
        const response = await fetch(apiUrl, {
          headers: {
            "Authorization": `Bearer ${accessToken}`,
            "Accept": "application/json"
          }
        });
        if (response.ok) {
          const data = await response.json();
          if (data.values && data.values.length > 0) {
            for (let i = 0; i < data.values.length; i++) {
              const row = data.values[i];
              if (row && row[0] && row[1]) {
                const product = {
                  id: `prod_${i + 1}`,
                  name: row[0].toString().trim(),
                  price: parseFloat(row[1]) || 0,
                  unit: row[2] ? row[2].toString().trim() : "\u500B"
                };
                if (product.name && product.price > 0) {
                  products.push(product);
                }
              }
            }
          }
          return new Response(JSON.stringify({
            success: true,
            products,
            sheetName: productSheetName,
            note: `\u5F9E\u5DE5\u4F5C\u8868\u300C${productSheetName}\u300D\u8B80\u53D6\u7684\u5546\u54C1\u6E05\u55AE`
          }), {
            headers: { "Content-Type": "application/json" }
          });
        }
      } catch (error) {
        console.error("OAuth \u8B80\u53D6\u5546\u54C1\u6E05\u55AE\u5931\u6557:", error);
      }
    }
    return new Response(JSON.stringify({
      success: true,
      products: [
        { id: "demo1", name: "\u7BC4\u4F8B\u5546\u54C1A", price: 100, unit: "\u76D2" },
        { id: "demo2", name: "\u7BC4\u4F8B\u5546\u54C1B", price: 200, unit: "\u74F6" },
        { id: "demo3", name: "\u7BC4\u4F8B\u5546\u54C1C", price: 150, unit: "\u5305" }
      ],
      note: "\u7121\u6CD5\u8B80\u53D6Excel\u6A94\u6848\uFF0C\u4F7F\u7528\u6A21\u64EC\u8CC7\u6599\u3002\u8ACB\u78BA\u8A8D\u6A94\u6848\u6B0A\u9650\u6216\u9032\u884COAuth\u8A8D\u8B49\u3002"
    }), {
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "\u7372\u53D6\u5546\u54C1\u6E05\u55AE\u6642\u767C\u751F\u932F\u8AA4: " + error.message
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}
__name(handleGetProductsFromExcel, "handleGetProductsFromExcel");
async function handlePreviewInvoice(request, env) {
  try {
    const url = new URL(request.url);
    const encodedData = url.searchParams.get("data");
    if (!encodedData) {
      return new Response("\u7F3A\u5C11\u51FA\u8CA8\u55AE\u8CC7\u6599", { status: 400 });
    }
    const data = JSON.parse(decodeURIComponent(encodedData));
    const html = generateInvoiceHTML(data);
    return new Response(html, {
      status: 200,
      headers: {
        "Content-Type": "text/html; charset=utf-8"
      }
    });
  } catch (error) {
    return new Response("\u89E3\u6790\u51FA\u8CA8\u55AE\u8CC7\u6599\u6642\u767C\u751F\u932F\u8AA4: " + error.message, { status: 500 });
  }
}
__name(handlePreviewInvoice, "handlePreviewInvoice");
function generateInvoiceHTML(data) {
  const invoiceNumber = "INV" + new Date(data.invoiceDate).getFullYear() + String(new Date(data.invoiceDate).getMonth() + 1).padStart(2, "0") + String(new Date(data.invoiceDate).getDate()).padStart(2, "0") + String(Math.floor(Math.random() * 1e3)).padStart(3, "0");
  let supplierInfo = {
    name: "\u60A8\u7684\u516C\u53F8\u540D\u7A31",
    phone: "06-2906741 / 0980347570",
    address: "\u53F0\u5357\u5E02\u6771\u5340\u5D07\u5B78\u8DEF165\u865F5\u6A13"
  };
  if (data.companyId === "liang-xin") {
    supplierInfo = {
      name: "\u91CF\u5FC3\u91AB\u85E5\u80A1\u4EFD\u6709\u9650\u516C\u53F8",
      phone: "06-2906741 / 0980347570",
      address: "\u53F0\u5357\u5E02\u6771\u5340\u5D07\u5B78\u8DEF165\u865F5\u6A13"
    };
  } else if (data.companyId === "jia-xuan") {
    supplierInfo = {
      name: "\u5609\u8431\u6F22\u65B9\u6709\u9650\u516C\u53F8",
      phone: "06-2906741 / 0980347570",
      address: "\u53F0\u5357\u5E02\u6771\u5340\u5D07\u5B78\u8DEF165\u865F5\u6A13"
    };
  }
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>\u51FA\u8CA8\u55AE - ${data.clientName}</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Microsoft YaHei', '\u5FAE\u8EDF\u6B63\u9ED1\u9AD4', Arial, sans-serif;
            background: #f5f7fa;
            padding: 20px;
            color: #333;
          }

          .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
          }

          .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
          }

          .invoice-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            letter-spacing: 2px;
          }

          .invoice-number {
            font-size: 14px;
            opacity: 0.9;
            position: absolute;
            top: 15px;
            right: 30px;
          }

          .invoice-body {
            padding: 40px;
          }

          .invoice-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 2px solid #f1f3f4;
          }

          .info-section h3 {
            color: #667eea;
            font-size: 16px;
            margin-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 5px;
          }

          .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
          }

          .info-label {
            color: #666;
            font-weight: 500;
          }

          .info-value {
            font-weight: 600;
            color: #333;
          }

          .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-radius: 8px;
            overflow: hidden;
          }

          .items-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            padding: 15px 20px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
          }

          .items-table td {
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
          }

          .items-table tbody tr:hover {
            background-color: #f8f9fa;
          }

          .items-table tbody tr:last-child td {
            border-bottom: none;
          }

          .text-right {
            text-align: right;
          }

          .text-center {
            text-align: center;
          }

          .amount {
            font-weight: 600;
            font-family: 'Courier New', monospace;
          }

          .summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
          }

          .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 16px;
          }

          .summary-row.total {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
            border-top: 2px solid #dee2e6;
            padding-top: 15px;
            margin-top: 20px;
            margin-bottom: 0;
          }

          .notes {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
          }

          .notes h4 {
            color: #856404;
            margin-bottom: 10px;
          }

          .notes p {
            color: #856404;
            line-height: 1.5;
            margin: 0;
          }

          .footer {
            text-align: center;
            color: #6c757d;
            font-size: 14px;
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
          }

          .actions {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
          }

          .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
          }

          .btn-primary {
            background: #667eea;
            color: white;
          }

          .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }

          .btn-secondary {
            background: #6c757d;
            color: white;
          }

          .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
          }

          @media print {
            body {
              background: white;
              padding: 0;
            }

            .invoice-container {
              box-shadow: none;
              border-radius: 0;
            }

            .actions, .btn {
              display: none !important;
            }

            .invoice-header {
              background: #667eea !important;
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }

            .summary {
              background: #f8f9fa !important;
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }
          }

          @media (max-width: 768px) {
            .invoice-info {
              grid-template-columns: 1fr;
              gap: 20px;
            }

            .items-table th,
            .items-table td {
              padding: 10px 15px;
            }

            .invoice-title {
              font-size: 24px;
            }

            .btn {
              display: block;
              margin: 10px auto;
              width: 200px;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="invoice-header">
            <div class="invoice-number">\u55AE\u865F: ${invoiceNumber}</div>
            <div class="invoice-title">\u51FA \u8CA8 \u55AE</div>
            <div>DELIVERY NOTE</div>
          </div>

          <div class="invoice-body">
            <div class="invoice-info">
              <div class="info-section">
                <h3>\u5BA2\u6236\u8CC7\u8A0A</h3>
                <div class="info-item">
                  <span class="info-label">\u5BA2\u6236\u540D\u7A31\uFF1A</span>
                  <span class="info-value">${data.clientName}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">\u51FA\u8CA8\u65E5\u671F\uFF1A</span>
                  <span class="info-value">${new Date(data.invoiceDate).toLocaleDateString("zh-TW")}</span>
                </div>
              </div>

              <div class="info-section">
                <h3>\u4F9B\u61C9\u5546\u8CC7\u8A0A</h3>
                <div class="info-item">
                  <span class="info-label">\u516C\u53F8\u540D\u7A31\uFF1A</span>
                  <span class="info-value">${supplierInfo.name}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">\u806F\u7D61\u96FB\u8A71\uFF1A</span>
                  <span class="info-value">${supplierInfo.phone}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">\u5730\u5740\uFF1A</span>
                  <span class="info-value">${supplierInfo.address}</span>
                </div>
              </div>
            </div>

            <table class="items-table">
              <thead>
                <tr>
                  <th style="width: 50px;">#</th>
                  <th>\u5546\u54C1\u540D\u7A31</th>
                  <th class="text-center" style="width: 100px;">\u6578\u91CF</th>
                  <th class="text-right" style="width: 120px;">\u55AE\u50F9</th>
                  <th class="text-right" style="width: 120px;">\u91D1\u984D</th>
                </tr>
              </thead>
              <tbody>
                ${data.items.map((item, index) => `
                  <tr>
                    <td class="text-center">${index + 1}</td>
                    <td>${item.name}</td>
                    <td class="text-center">${item.quantity}</td>
                    <td class="text-right amount">$${item.unitPrice.toLocaleString("zh-TW", { minimumFractionDigits: 2 })}</td>
                    <td class="text-right amount">$${item.amount.toLocaleString("zh-TW", { minimumFractionDigits: 2 })}</td>
                  </tr>
                `).join("")}
              </tbody>
            </table>

            <div class="summary">
              <div class="summary-row">
                <span>\u5546\u54C1\u5C0F\u8A08\uFF1A</span>
                <span class="amount">$${data.subtotal.toLocaleString("zh-TW", { minimumFractionDigits: 2 })}</span>
              </div>
              <div class="summary-row">
                <span>\u904B\u8CBB\uFF1A</span>
                <span class="amount">$${data.shippingFee.toLocaleString("zh-TW", { minimumFractionDigits: 2 })}</span>
              </div>
              <div class="summary-row total">
                <span>\u672C\u6B21\u61C9\u4ED8\u7E3D\u8A08\uFF1A</span>
                <span class="amount">$${data.total.toLocaleString("zh-TW", { minimumFractionDigits: 2 })}</span>
              </div>
            </div>

            <div class="summary" style="background: #f0f8ff; border-left-color: #007bff;">
              <h4 style="color: #007bff; margin-bottom: 15px;">\u5E33\u52D9\u8A08\u7B97</h4>
              <div class="summary-row">
                <span>\u524D\u671F\u9918\u984D\uFF1A</span>
                <span class="amount">$${(data.previousBalance || 0).toLocaleString("zh-TW", { minimumFractionDigits: 2 })}</span>
              </div>
              <div class="summary-row">
                <span>\u6E1B\uFF1A\u672C\u6B21\u61C9\u4ED8\uFF1A</span>
                <span class="amount">$${data.total.toLocaleString("zh-TW", { minimumFractionDigits: 2 })}</span>
              </div>
              <div class="summary-row total" style="background: ${data.balance === 0 ? "#d4edda" : data.balance < 0 ? "#f8d7da" : "#fff3cd"}; padding: 10px; border-radius: 5px;">
                <span style="color: ${data.balance === 0 ? "#155724" : data.balance < 0 ? "#721c24" : "#856404"};">
                  ${data.balance === 0 ? "\u2713 \u5DF2\u7D50\u6E05" : data.balance < 0 ? "\u5BA2\u6236\u6B20\u6B3E\uFF1A" : "\u5BA2\u6236\u9918\u984D\uFF1A"}
                </span>
                <span class="amount" style="color: ${data.balance === 0 ? "#155724" : data.balance < 0 ? "#721c24" : "#856404"}; font-size: 22px;">
                  $${data.balance < 0 ? data.balance.toLocaleString("zh-TW", { minimumFractionDigits: 2 }) : (data.balance || 0).toLocaleString("zh-TW", { minimumFractionDigits: 2 })}
                </span>
              </div>
            </div>

            ${data.notes ? `
              <div class="notes">
                <h4>\u{1F4DD} \u5099\u8A3B</h4>
                <p>${data.notes}</p>
              </div>
            ` : ""}
          </div>

          <div class="actions">
            <button class="btn btn-primary" onclick="window.print()">\u{1F5A8}\uFE0F \u5217\u5370\u51FA\u8CA8\u55AE</button>
            <button class="btn btn-secondary" onclick="window.close()">\u2715 \u95DC\u9589\u8996\u7A97</button>
          </div>

          <div class="footer">
            <p>\u6B64\u51FA\u8CA8\u55AE\u7531\u7CFB\u7D71\u81EA\u52D5\u751F\u6210 | \u751F\u6210\u6642\u9593\uFF1A${(/* @__PURE__ */ new Date()).toLocaleString("zh-TW")}</p>
          </div>
        </div>
      </body>
    </html>
  `;
}
__name(generateInvoiceHTML, "generateInvoiceHTML");
function getEmbeddedSheetPage(fileId, clientName) {
  const embedUrl = `https://docs.google.com/spreadsheets/d/${fileId}/edit?rm=minimal&headers=false&widget=false`;
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>${clientName ? clientName + " - " : ""}Google Sheets \u7DE8\u8F2F\u5668</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
          }

          .header {
            background: #1a73e8;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }

          .header h1 {
            font-size: 20px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
          }

          .client-name {
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
          }

          .actions {
            display: flex;
            gap: 10px;
          }

          .btn {
            background: white;
            color: #1a73e8;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
          }

          .btn:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transform: translateY(-1px);
          }

          .iframe-container {
            flex: 1;
            position: relative;
            background: white;
            margin: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
          }

          iframe {
            width: 100%;
            height: 100%;
            border: none;
          }

          .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
          }

          .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1a73e8;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          @media (max-width: 768px) {
            .header {
              flex-direction: column;
              gap: 10px;
              text-align: center;
            }

            .iframe-container {
              margin: 10px;
            }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="8" y1="13" x2="16" y2="13"></line>
              <line x1="8" y1="17" x2="16" y2="17"></line>
            </svg>
            Google Sheets \u7DE8\u8F2F\u5668
            ${clientName ? `<span class="client-name">${clientName}</span>` : ""}
          </h1>
          <div class="actions">
            <a href="https://docs.google.com/spreadsheets/d/${fileId}/edit" target="_blank" class="btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                <polyline points="15 3 21 3 21 9"></polyline>
                <line x1="10" y1="14" x2="21" y2="3"></line>
              </svg>
              \u5728\u65B0\u8996\u7A97\u958B\u555F
            </a>
            <button onclick="location.reload()" class="btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23 4 23 10 17 10"></polyline>
                <polyline points="1 20 1 14 7 14"></polyline>
                <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
              </svg>
              \u91CD\u65B0\u6574\u7406
            </button>
          </div>
        </div>

        <div class="iframe-container">
          <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>\u6B63\u5728\u8F09\u5165 Google Sheets...</p>
          </div>
          <iframe
            id="sheet-iframe"
            src="${embedUrl}"
            onload="document.getElementById('loading').style.display='none'"
            onerror="handleError()"
            allowfullscreen>
          </iframe>
        </div>

        <script>
          function handleError() {
            document.getElementById('loading').innerHTML =
              '<p style="color: #d93025;">\u7121\u6CD5\u8F09\u5165 Google Sheets</p>' +
              '<p style="margin-top: 10px; font-size: 14px;">\u8ACB\u78BA\u8A8D\u60A8\u6709\u5B58\u53D6\u6B0A\u9650</p>' +
              '<button onclick="location.reload()" class="btn" style="margin-top: 20px;">\u91CD\u8A66</button>';
          }

          // \u8D85\u6642\u8655\u7406
          setTimeout(function() {
            const loading = document.getElementById('loading');
            if (loading && loading.style.display !== 'none') {
              loading.innerHTML =
                '<p style="color: #ff9800;">\u8F09\u5165\u6642\u9593\u8F03\u9577...</p>' +
                '<p style="margin-top: 10px; font-size: 14px;">\u5982\u679C\u7121\u6CD5\u8F09\u5165\uFF0C\u8ACB\u6AA2\u67E5\u60A8\u7684\u7DB2\u8DEF\u9023\u7DDA</p>';
            }
          }, 10000);
        <\/script>
      </body>
    </html>
  `;
}
__name(getEmbeddedSheetPage, "getEmbeddedSheetPage");

// node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-vFfCd4/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-vFfCd4/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof __Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
__name(__Facade_ScheduledController__, "__Facade_ScheduledController__");
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = (request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    };
    #dispatcher = (type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    };
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
