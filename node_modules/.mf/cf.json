{"clientTcpRtt": 2, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 3462, "clientAcceptEncoding": "gzip, deflate, br", "verifiedBotCategory": "", "country": "TW", "isEUCountry": false, "region": "Taiwan", "tlsClientCiphersSha1": "kXrN3VEKDdzz2cPKTQaKzpxVTxQ=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "3Ywb+GSuysVtWIyG3xSokC+T1m1SDLl1uZ3ximLnzkg=", "tlsExportedAuthenticator": {"clientFinished": "11334b5b1ebe373203fae0146726afd1d774bc175e5762ce3b9027e92a89168c2f73e175989766d1e8a70b71e54c9e4d", "clientHandshake": "9eae8b5f709f084811fd35d51f6b82d7ab0397570b72e8a193ac805c82339a63acf32ed2e29c0a56ce354238da525b1b", "serverHandshake": "474bc4e59c709914188787491b2f1ca77dd53859aa8d537a8f2d1f263f2ac368d55815a1905469f8a93c24a76179ac6e", "serverFinished": "6dab5cd5fc53edf22abe5edb847966f944f06f323b3faeca5b3130435de175fa9533b92addfb507ec32cb2ea3b098f6e"}, "tlsClientHelloLength": "1605", "colo": "KHH", "timezone": "Asia/Taipei", "longitude": "120.21333", "latitude": "22.99083", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "city": "Tainan", "tlsVersion": "TLSv1.3", "regionCode": "04", "asOrganization": "Chunghwa Telecom Co.,Ltd.", "tlsClientExtensionsSha1Le": "u4wtEMFQBY18l3BzHAvORm+KGRw=", "tlsClientExtensionsSha1": "1eY97BUYYO8vDaTfHQywB1pcNdM=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}