"use strict";

var _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"]) _i["return"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError("Invalid attempt to destructure non-iterable instance"); } }; }();

const ansiEscapeCode = '[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-PRZcf-nqry=><]',
      zeroWidthCharacterExceptNewline = '\u0000-\u0008\u000B-\u0019\u001b\u009b\u00ad\u200b\u2028\u2029\ufeff\ufe00-\ufe0f',
      zeroWidthCharacter = '\n' + zeroWidthCharacterExceptNewline,
      zeroWidthCharactersExceptNewline = new RegExp('(?:' + ansiEscapeCode + ')|[' + zeroWidthCharacterExceptNewline + ']', 'g'),
      zeroWidthCharacters = new RegExp('(?:' + ansiEscapeCode + ')|[' + zeroWidthCharacter + ']', 'g'),
      partition = new RegExp('((?:' + ansiEscapeCode + ')|[\t' + zeroWidthCharacter + '])?([^\t' + zeroWidthCharacter + ']*)', 'g');

module.exports = {

    zeroWidthCharacters,

    ansiEscapeCodes: new RegExp(ansiEscapeCode, 'g'),

    strlen: s => Array.from(s.replace(zeroWidthCharacters, '')).length, // Array.from solves the emoji problem as described here: http://blog.jonnew.com/posts/poo-dot-length-equals-two

    isBlank: s => s.replace(zeroWidthCharacters, '').replace(/\s/g, '').length === 0,

    blank: s => Array.from(s.replace(zeroWidthCharactersExceptNewline, '')) // Array.from solves the emoji problem as described here: http://blog.jonnew.com/posts/poo-dot-length-equals-two
    .map(x => x === '\t' || x === '\n' ? x : ' ').join(''),

    partition(s) {
        for (var m, spans = []; partition.lastIndex !== s.length && (m = partition.exec(s));) {
            spans.push([m[1] || '', m[2]]);
        }
        partition.lastIndex = 0; // reset
        return spans;
    },

    first(s, n) {

        let result = '',
            length = 0;

        for (const _ref of module.exports.partition(s)) {
            var _ref2 = _slicedToArray(_ref, 2);

            const nonPrintable = _ref2[0];
            const printable = _ref2[1];

            const text = Array.from(printable).slice(0, n - length); // Array.from solves the emoji problem as described here: http://blog.jonnew.com/posts/poo-dot-length-equals-two
            result += nonPrintable + text.join('');
            length += text.length;
        }

        return result;
    }
};

//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4uL3ByaW50YWJsZS1jaGFyYWN0ZXJzLmpzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7O0FBRUEsTUFBTSxpQkFBbUMsNEVBQXpDO0FBQUEsTUFDTSxrQ0FBbUMsbUZBRHpDO0FBQUEsTUFFTSxxQkFBbUMsT0FBTywrQkFGaEQ7QUFBQSxNQUdNLG1DQUFtQyxJQUFJLE1BQUosQ0FBWSxRQUFRLGNBQVIsR0FBeUIsS0FBekIsR0FBaUMsK0JBQWpDLEdBQW1FLEdBQS9FLEVBQW9GLEdBQXBGLENBSHpDO0FBQUEsTUFJTSxzQkFBbUMsSUFBSSxNQUFKLENBQVksUUFBUSxjQUFSLEdBQXlCLEtBQXpCLEdBQWlDLGtCQUFqQyxHQUFzRCxHQUFsRSxFQUF1RSxHQUF2RSxDQUp6QztBQUFBLE1BS00sWUFBbUMsSUFBSSxNQUFKLENBQVksU0FBUyxjQUFULEdBQTBCLE9BQTFCLEdBQW9DLGtCQUFwQyxHQUF5RCxVQUF6RCxHQUFzRSxrQkFBdEUsR0FBMkYsS0FBdkcsRUFBOEcsR0FBOUcsQ0FMekM7O0FBT0EsT0FBTyxPQUFQLEdBQWlCOztBQUViLHVCQUZhOztBQUliLHFCQUFpQixJQUFJLE1BQUosQ0FBWSxjQUFaLEVBQTRCLEdBQTVCLENBSko7O0FBTWIsWUFBUSxLQUFLLE1BQU0sSUFBTixDQUFZLEVBQUUsT0FBRixDQUFXLG1CQUFYLEVBQWdDLEVBQWhDLENBQVosRUFBaUQsTUFOakQsRUFNeUQ7O0FBRXRFLGFBQVMsS0FBSyxFQUFFLE9BQUYsQ0FBVyxtQkFBWCxFQUFnQyxFQUFoQyxFQUNFLE9BREYsQ0FDVyxLQURYLEVBQ2tCLEVBRGxCLEVBRUUsTUFGRixLQUVhLENBVmQ7O0FBWWIsV0FBTyxLQUFLLE1BQU0sSUFBTixDQUFZLEVBQUUsT0FBRixDQUFXLGdDQUFYLEVBQTZDLEVBQTdDLENBQVosRUFBOEQ7QUFBOUQsS0FDTSxHQUROLENBQ1csS0FBTyxNQUFNLElBQVAsSUFBaUIsTUFBTSxJQUF4QixHQUFpQyxDQUFqQyxHQUFxQyxHQURyRCxFQUVNLElBRk4sQ0FFWSxFQUZaLENBWkM7O0FBZ0JiLGNBQVcsQ0FBWCxFQUFjO0FBQ1YsYUFBSyxJQUFJLENBQUosRUFBTyxRQUFRLEVBQXBCLEVBQXlCLFVBQVUsU0FBVixLQUF3QixFQUFFLE1BQTNCLEtBQXVDLElBQUksVUFBVSxJQUFWLENBQWdCLENBQWhCLENBQTNDLENBQXhCLEdBQXlGO0FBQUUsa0JBQU0sSUFBTixDQUFZLENBQUMsRUFBRSxDQUFGLEtBQVEsRUFBVCxFQUFhLEVBQUUsQ0FBRixDQUFiLENBQVo7QUFBaUM7QUFDNUgsa0JBQVUsU0FBVixHQUFzQixDQUF0QixDQUZVLENBRWM7QUFDeEIsZUFBTyxLQUFQO0FBQ0gsS0FwQlk7O0FBc0JiLFVBQU8sQ0FBUCxFQUFVLENBQVYsRUFBYTs7QUFFVCxZQUFJLFNBQVMsRUFBYjtBQUFBLFlBQWlCLFNBQVMsQ0FBMUI7O0FBRUEsMkJBQXdDLE9BQU8sT0FBUCxDQUFlLFNBQWYsQ0FBMEIsQ0FBMUIsQ0FBeEMsRUFBc0U7QUFBQTs7QUFBQSxrQkFBMUQsWUFBMEQ7QUFBQSxrQkFBNUMsU0FBNEM7O0FBQ2xFLGtCQUFNLE9BQU8sTUFBTSxJQUFOLENBQVksU0FBWixFQUF1QixLQUF2QixDQUE4QixDQUE5QixFQUFpQyxJQUFJLE1BQXJDLENBQWIsQ0FEa0UsQ0FDUjtBQUMxRCxzQkFBVSxlQUFlLEtBQUssSUFBTCxDQUFXLEVBQVgsQ0FBekI7QUFDQSxzQkFBVSxLQUFLLE1BQWY7QUFDSDs7QUFFRCxlQUFPLE1BQVA7QUFDSDtBQWpDWSxDQUFqQiIsImZpbGUiOiJwcmludGFibGUtY2hhcmFjdGVycy5qcyIsInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5jb25zdCBhbnNpRXNjYXBlQ29kZSAgICAgICAgICAgICAgICAgICA9ICdbXFx1MDAxYlxcdTAwOWJdW1soKSM7P10qKD86WzAtOV17MSw0fSg/OjtbMC05XXswLDR9KSopP1swLTlBLVBSWmNmLW5xcnk9PjxdJ1xuICAgICwgemVyb1dpZHRoQ2hhcmFjdGVyRXhjZXB0TmV3bGluZSAgPSAnXFx1MDAwMC1cXHUwMDA4XFx1MDAwQi1cXHUwMDE5XFx1MDAxYlxcdTAwOWJcXHUwMGFkXFx1MjAwYlxcdTIwMjhcXHUyMDI5XFx1ZmVmZlxcdWZlMDAtXFx1ZmUwZidcbiAgICAsIHplcm9XaWR0aENoYXJhY3RlciAgICAgICAgICAgICAgID0gJ1xcbicgKyB6ZXJvV2lkdGhDaGFyYWN0ZXJFeGNlcHROZXdsaW5lXG4gICAgLCB6ZXJvV2lkdGhDaGFyYWN0ZXJzRXhjZXB0TmV3bGluZSA9IG5ldyBSZWdFeHAgKCcoPzonICsgYW5zaUVzY2FwZUNvZGUgKyAnKXxbJyArIHplcm9XaWR0aENoYXJhY3RlckV4Y2VwdE5ld2xpbmUgKyAnXScsICdnJylcbiAgICAsIHplcm9XaWR0aENoYXJhY3RlcnMgICAgICAgICAgICAgID0gbmV3IFJlZ0V4cCAoJyg/OicgKyBhbnNpRXNjYXBlQ29kZSArICcpfFsnICsgemVyb1dpZHRoQ2hhcmFjdGVyICsgJ10nLCAnZycpXG4gICAgLCBwYXJ0aXRpb24gICAgICAgICAgICAgICAgICAgICAgICA9IG5ldyBSZWdFeHAgKCcoKD86JyArIGFuc2lFc2NhcGVDb2RlICsgJyl8W1xcdCcgKyB6ZXJvV2lkdGhDaGFyYWN0ZXIgKyAnXSk/KFteXFx0JyArIHplcm9XaWR0aENoYXJhY3RlciArICddKiknLCAnZycpXG5cbm1vZHVsZS5leHBvcnRzID0ge1xuXG4gICAgemVyb1dpZHRoQ2hhcmFjdGVycyxcblxuICAgIGFuc2lFc2NhcGVDb2RlczogbmV3IFJlZ0V4cCAoYW5zaUVzY2FwZUNvZGUsICdnJyksXG5cbiAgICBzdHJsZW46IHMgPT4gQXJyYXkuZnJvbSAocy5yZXBsYWNlICh6ZXJvV2lkdGhDaGFyYWN0ZXJzLCAnJykpLmxlbmd0aCwgLy8gQXJyYXkuZnJvbSBzb2x2ZXMgdGhlIGVtb2ppIHByb2JsZW0gYXMgZGVzY3JpYmVkIGhlcmU6IGh0dHA6Ly9ibG9nLmpvbm5ldy5jb20vcG9zdHMvcG9vLWRvdC1sZW5ndGgtZXF1YWxzLXR3b1xuXG4gICAgaXNCbGFuazogcyA9PiBzLnJlcGxhY2UgKHplcm9XaWR0aENoYXJhY3RlcnMsICcnKVxuICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlICgvXFxzL2csICcnKVxuICAgICAgICAgICAgICAgICAgIC5sZW5ndGggPT09IDAsXG5cbiAgICBibGFuazogcyA9PiBBcnJheS5mcm9tIChzLnJlcGxhY2UgKHplcm9XaWR0aENoYXJhY3RlcnNFeGNlcHROZXdsaW5lLCAnJykpIC8vIEFycmF5LmZyb20gc29sdmVzIHRoZSBlbW9qaSBwcm9ibGVtIGFzIGRlc2NyaWJlZCBoZXJlOiBodHRwOi8vYmxvZy5qb25uZXcuY29tL3Bvc3RzL3Bvby1kb3QtbGVuZ3RoLWVxdWFscy10d29cbiAgICAgICAgICAgICAgICAgICAgIC5tYXAgKHggPT4gKCh4ID09PSAnXFx0JykgfHwgKHggPT09ICdcXG4nKSkgPyB4IDogJyAnKVxuICAgICAgICAgICAgICAgICAgICAgLmpvaW4gKCcnKSxcblxuICAgIHBhcnRpdGlvbiAocykge1xuICAgICAgICBmb3IgKHZhciBtLCBzcGFucyA9IFtdOyAocGFydGl0aW9uLmxhc3RJbmRleCAhPT0gcy5sZW5ndGgpICYmIChtID0gcGFydGl0aW9uLmV4ZWMgKHMpKTspIHsgc3BhbnMucHVzaCAoW21bMV0gfHwgJycsIG1bMl1dKSB9XG4gICAgICAgIHBhcnRpdGlvbi5sYXN0SW5kZXggPSAwIC8vIHJlc2V0XG4gICAgICAgIHJldHVybiBzcGFuc1xuICAgIH0sXG5cbiAgICBmaXJzdCAocywgbikge1xuXG4gICAgICAgIGxldCByZXN1bHQgPSAnJywgbGVuZ3RoID0gMFxuXG4gICAgICAgIGZvciAoY29uc3QgW25vblByaW50YWJsZSwgcHJpbnRhYmxlXSBvZiBtb2R1bGUuZXhwb3J0cy5wYXJ0aXRpb24gKHMpKSB7XG4gICAgICAgICAgICBjb25zdCB0ZXh0ID0gQXJyYXkuZnJvbSAocHJpbnRhYmxlKS5zbGljZSAoMCwgbiAtIGxlbmd0aCkgLy8gQXJyYXkuZnJvbSBzb2x2ZXMgdGhlIGVtb2ppIHByb2JsZW0gYXMgZGVzY3JpYmVkIGhlcmU6IGh0dHA6Ly9ibG9nLmpvbm5ldy5jb20vcG9zdHMvcG9vLWRvdC1sZW5ndGgtZXF1YWxzLXR3b1xuICAgICAgICAgICAgcmVzdWx0ICs9IG5vblByaW50YWJsZSArIHRleHQuam9pbiAoJycpXG4gICAgICAgICAgICBsZW5ndGggKz0gdGV4dC5sZW5ndGhcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiByZXN1bHRcbiAgICB9XG59Il19