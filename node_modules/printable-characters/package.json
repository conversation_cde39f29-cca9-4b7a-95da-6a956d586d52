{"name": "printable-characters", "version": "1.0.42", "description": "A little helper for handling strings containing zero width control characters, ANSI styling, whitespaces, newlines, 💩, etc.", "main": "./build/printable-characters.js", "scripts": {"lint": "eslint printable-characters.js", "lint-test": "eslint printable-characters.js", "babel": "babel printable-characters.js --source-maps inline --out-file ./build/printable-characters.js", "build": "npm run lint && npm run lint-test && npm run babel", "test": "npm run build && env PRINTABLE_CHARACTERS_TEST_FILE=./build/printable-characters.js nyc --reporter=html --reporter=text mocha --reporter spec", "autotest": "env PRINTABLE_CHARACTERS_TEST_FILE=./printable-characters.js mocha --reporter spec --watch", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "https://github.com/xpl/printable-characters.git"}, "keywords": ["string width", "string length", "real string width", "real string length", "optical string width", "printed width", "unicode", "codepoints", "code points", "strlen", "zero width", "zero width symbols", "zero width characters", "visible characters", "visible symbols", "visible", "invisible", "invisible symbols", "invisible characters", "printable", "printable length", "printable symbols", "printable characters", "non-printable characters", "nonprintable characters", "characters", "symbols", "string length", "real string length", "string trimming", "trimming", "escapes", "escape codes", "codes", "an<PERSON> escapes", "tokenizing", "ansi", "whitespaces"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "Unlicense", "homepage": "https://github.com/xpl/printable-characters", "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-es2015-block-scoped-functions": "^6.22.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "coveralls": "^2.13.3", "eslint": "^4.8.0", "istanbul": "^0.4.5", "mocha": "^3.5.3", "nyc": "^11.2.1"}}